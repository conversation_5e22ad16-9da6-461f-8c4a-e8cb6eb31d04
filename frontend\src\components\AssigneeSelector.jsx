import React, { useState, useEffect } from 'react';
import { Avatar, Button, Popover, Spin } from 'antd';
import { PlusOutlined, CloseOutlined, UserOutlined } from '@ant-design/icons';
import { useOutletContext } from 'react-router-dom';
import assignService from '../services/assignTaskService';
import { getBoardAssignees } from '../services/boardAssignService';

const AssigneeSelector = ({
    task,
    boardId,
    isCompleted = false,
    size = 'small',
    boardMembers = null, // Truyền từ parent để tránh fetch lại
    onPopoverStateChange = null // Callback để thông báo trạng thái popover
}) => {
    const [assigneePopoverVisible, setAssigneePopoverVisible] = useState(false);

    const { workspaces, selectedWorkspace } = useOutletContext();

    const currentWorkspace = workspaces.find(
        (ws) => ws.id === selectedWorkspace
    );

    const isUserWorkspaceOwner = currentWorkspace?.isOwned || false;

    // Cập nhật state popover và thông báo cho parent
    const updatePopoverState = (visible) => {
        setAssigneePopoverVisible(visible);
        if (onPopoverStateChange) {
            onPopoverStateChange(visible);
        }
    };
    const [assigneeList, setAssigneeList] = useState([]);
    const [internalBoardMembers, setInternalBoardMembers] = useState([]);
    const [isLoadingBoardMembers, setIsLoadingBoardMembers] = useState(false);

    // Khởi tạo assigneeList từ task và load danh sách assignees hiện tại
    useEffect(() => {
        const loadTaskAssignees = async () => {
            try {
                const assignees = await assignService.getTaskAssignees(task.id);
                setAssigneeList(assignees || []);
            } catch (error) {
                console.error('Error loading task assignees:', error);
                setAssigneeList([]);
            }
        };

        if (task.id) {
            loadTaskAssignees();
        }
    }, [task.id]);

    // Lắng nghe sự kiện workspace member removal để refresh task assignees
    useEffect(() => {
        const handleWorkspaceMemberRemoved = (event) => {
            const { workspaceId } = event.detail;
            // Kiểm tra xem task này có thuộc workspace hiện tại không
            if (selectedWorkspace === workspaceId) {
                // Refresh task assignees
                const loadTaskAssignees = async () => {
                    try {
                        const assignees = await assignService.getTaskAssignees(
                            task.id
                        );
                        setAssigneeList(assignees || []);
                    } catch (error) {
                        console.error(
                            'Error refreshing task assignees:',
                            error
                        );
                    }
                };
                loadTaskAssignees();
            }
        };

        window.addEventListener(
            'workspace:member_removed',
            handleWorkspaceMemberRemoved
        );

        return () => {
            window.removeEventListener(
                'workspace:member_removed',
                handleWorkspaceMemberRemoved
            );
        };
    }, [task.id, selectedWorkspace]);

    // Load danh sách thành viên board nếu không được truyền từ parent
    useEffect(() => {
        if (boardMembers !== null) {
            // Sử dụng boardMembers từ parent
            setInternalBoardMembers(boardMembers);
            setIsLoadingBoardMembers(false);
            return;
        }

        const loadBoardMembers = async () => {
            if (!boardId) return;

            setIsLoadingBoardMembers(true);
            try {
                const members = await getBoardAssignees(boardId);
                setInternalBoardMembers(members || []);
            } catch (error) {
                console.error('Error loading board members:', error);
                setInternalBoardMembers([]);
            } finally {
                setIsLoadingBoardMembers(false);
            }
        };

        loadBoardMembers();
    }, [boardId, boardMembers]);

    const handleAddAssignee = async (user) => {
        try {
            await assignService.addAssigneeToTask(task.id, user.email);

            // Reload assignees
            const updatedAssignees = await assignService.getTaskAssignees(
                task.id
            );
            setAssigneeList(updatedAssignees);

            updatePopoverState(false);
        } catch (error) {
            console.error('Error adding assignee:', error);
        }
    };

    const handleRemoveAssignee = async (assignee) => {
        try {
            const email = assignee.email || assignee.user?.email;
            await assignService.removeAssigneeFromTask(task.id, email);

            // Reload assignees
            const updatedAssignees = await assignService.getTaskAssignees(
                task.id
            );
            setAssigneeList(updatedAssignees);
        } catch (error) {
            console.error('Error removing assignee:', error);
        }
    };

    // Kiểm tra xem người dùng đã được gán cho task chưa
    const isUserAssigned = (user) => {
        const userEmail = user.email || user.user?.email;
        return assigneeList.some(
            (assignee) => (assignee.email || assignee.user?.email) === userEmail
        );
    };

    const renderBoardMembersList = () => {
        if (isLoadingBoardMembers) {
            return (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                    <Spin size='small' />
                    <div
                        style={{
                            marginTop: '8px',
                            fontSize: '13px',
                            color: '#8c8c8c'
                        }}
                    >
                        Đang tải danh sách...
                    </div>
                </div>
            );
        }

        if (internalBoardMembers.length === 0) {
            return (
                <div
                    style={{
                        textAlign: 'center',
                        color: '#8c8c8c',
                        padding: '20px 0',
                        fontSize: '13px'
                    }}
                >
                    Chưa có thành viên trong bảng
                </div>
            );
        }

        return internalBoardMembers.map((member) => {
            const user = member.user || member;
            const isAssigned = isUserAssigned(user);

            return (
                <div
                    key={member.id || user.id}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px',
                        cursor: isAssigned ? 'default' : 'pointer',
                        borderRadius: '4px',
                        transition: 'background-color 0.2s',
                        opacity: isAssigned ? 0.6 : 1,
                        backgroundColor: isAssigned ? '#f0f0f0' : 'transparent'
                    }}
                    onClick={() => {
                        if (!isAssigned) {
                            handleAddAssignee(user);
                        }
                    }}
                    onMouseEnter={(e) => {
                        if (!isAssigned) {
                            e.currentTarget.style.backgroundColor = '#f5f5f5';
                        }
                    }}
                    onMouseLeave={(e) => {
                        if (!isAssigned) {
                            e.currentTarget.style.backgroundColor =
                                'transparent';
                        }
                    }}
                >
                    <Avatar
                        src={user.photoUrl || user.photo_url}
                        style={{
                            backgroundColor: '#1890ff',
                            marginRight: '12px'
                        }}
                        size='small'
                    >
                        {!user.photoUrl &&
                            !user.photo_url &&
                            (user.fullName || user.full_name
                                ? (user.fullName || user.full_name)
                                      .charAt(0)
                                      .toUpperCase()
                                : user.email.charAt(0).toUpperCase())}
                    </Avatar>
                    <div style={{ flex: 1 }}>
                        <div style={{ fontWeight: 500, fontSize: '14px' }}>
                            {user.fullName || user.full_name || user.email}
                        </div>
                        {(user.fullName || user.full_name) && (
                            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                                {user.email}
                            </div>
                        )}
                    </div>
                    {isAssigned && (
                        <div
                            style={{
                                color: '#52c41a',
                                fontSize: '12px',
                                marginLeft: '8px'
                            }}
                        >
                            Đã gán
                        </div>
                    )}
                </div>
            );
        });
    };

    const assigneeContent = (
        <div style={{ width: 280 }}>
            <div
                style={{
                    padding: '8px 0',
                    borderBottom: '1px solid #f0f0f0',
                    marginBottom: '8px'
                }}
            >
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}
                >
                    <span style={{ fontWeight: 500 }}>
                        Gán thành viên board
                    </span>
                    <CloseOutlined
                        onClick={() => {
                            updatePopoverState(false);
                        }}
                        style={{ cursor: 'pointer' }}
                    />
                </div>
            </div>

            <div
                style={{
                    minHeight: '100px',
                    maxHeight: '200px',
                    overflowY: 'auto'
                }}
            >
                {renderBoardMembersList()}
            </div>
        </div>
    );

    const getAssigneeDisplay = () => {
        if (assigneeList.length === 0) {
            return (
                <>
                    {isUserWorkspaceOwner && (
                        <Popover
                            content={assigneeContent}
                            title={null}
                            trigger='click'
                            open={assigneePopoverVisible}
                            onOpenChange={updatePopoverState}
                            placement='bottomLeft'
                        >
                            <Button
                                type='text'
                                icon={<PlusOutlined />}
                                size={size}
                                shape='circle'
                                style={{
                                    width: size === 'small' ? '24px' : '32px',
                                    height: size === 'small' ? '24px' : '32px',
                                    minWidth: 'unset',
                                    color: isCompleted ? '#8c8c8c' : '#bfbfbf'
                                }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    updatePopoverState(true);
                                }}
                                title='Thêm người thực hiện'
                            />
                        </Popover>
                    )}
                </>
            );
        }

        return (
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                {assigneeList.map((assignee) => {
                    const user = assignee.user || assignee;
                    return (
                        <div
                            key={assignee.id || user.id}
                            style={{
                                position: 'relative',
                                display: 'inline-block'
                            }}
                            className='assignee-avatar-container'
                        >
                            <Avatar
                                src={user.photoUrl || user.photo_url}
                                style={{
                                    backgroundColor: '#1890ff',
                                    opacity: isCompleted ? 0.6 : 1,
                                    border: '1px solid transparent',
                                    transition: 'all 0.2s ease'
                                }}
                                size={size}
                                title={user.fullName}
                            >
                                {!user.photoUrl &&
                                    !user.photo_url &&
                                    (user.fullName || user.full_name
                                        ? (user.fullName || user.full_name)
                                              .charAt(0)
                                              .toUpperCase()
                                        : user.email.charAt(0).toUpperCase())}
                            </Avatar>
                            {isUserWorkspaceOwner && (
                                <div>
                                    <div
                                        className='remove-assignee-btn'
                                        style={{
                                            position: 'absolute',
                                            top: '-4px',
                                            right: '-4px',
                                            width: '16px',
                                            height: '16px',
                                            backgroundColor: '#ff4d4f',
                                            borderRadius: '50%',
                                            display: 'none',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            cursor: 'pointer',
                                            fontSize: '10px',
                                            color: 'white',
                                            border: '2px solid white',
                                            boxShadow:
                                                '0 2px 4px rgba(0,0,0,0.2)',
                                            zIndex: 10,
                                            transition: 'all 0.2s ease'
                                        }}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleRemoveAssignee(assignee);
                                        }}
                                        title='Bỏ gán'
                                    >
                                        ×
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}

                {/* Nút thêm assignee */}

                {isUserWorkspaceOwner && (
                    <Popover
                        content={assigneeContent}
                        title={null}
                        trigger='click'
                        open={assigneePopoverVisible}
                        onOpenChange={updatePopoverState}
                        placement='bottomLeft'
                    >
                        <Button
                            type='text'
                            icon={<PlusOutlined />}
                            size={size}
                            shape='circle'
                            style={{
                                width: size === 'small' ? '24px' : '32px',
                                height: size === 'small' ? '24px' : '32px',
                                minWidth: 'unset',
                                color: isCompleted ? '#8c8c8c' : '#bfbfbf'
                            }}
                            onClick={(e) => {
                                e.stopPropagation();
                                updatePopoverState(true);
                            }}
                            title='Thêm người thực hiện'
                        />
                    </Popover>
                )}
            </div>
        );
    };

    return getAssigneeDisplay();
};

export default AssigneeSelector;
