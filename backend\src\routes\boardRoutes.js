const express = require('express');
const router = express.Router();
const BoardController = require('../controllers/boardController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

router.get('/',isAuthenticated, handleTokenExpiration, BoardController.getBoards);
router.post('/',isAuthenticated, handleTokenExpiration, BoardController.createBoard);
router.put('/:id',isAuthenticated, handleTokenExpiration, BoardController.updateBoard);
router.delete('/:id',isAuthenticated, handleTokenExpiration, BoardController.deleteBoard);
router.get('/:userId',isAuthenticated, handleTokenExpiration, BoardController.getBoardsByUserId);
router.get('/workspace/:workspaceId',isAuthenticated, handleTokenExpiration, BoardController.getBoardByWorkspaceId);

module.exports = router;