import { useState, useEffect } from "react";
import TaskService from "../services/taskService";
import { useMessage } from "../contexts/MessageContext";
import { useSocket } from "../contexts/SocketContext";

// Utility function để tổ chức tasks thành cấu trúc 1 cấp: task cha và task con
const organizeTasksHierarchy = (tasks) => {
    if (!tasks || tasks.length === 0) return [];

    // Tạo map để dễ dàng tìm kiếm parent tasks
    const taskMap = new Map();
    tasks.forEach((task) => {
        taskMap.set(task.id, { ...task, children: [] });
    });

    // Phân loại tasks thành parent và child (chỉ 1 cấp)
    const rootTasks = [];

    tasks.forEach((task) => {
        const currentTask = taskMap.get(task.id);

        if (task.parentTaskId || task.parent_task_id || task.parent) {
            // Đây là task con (chỉ cấp 1)
            const parentId =
                task.parentTaskId || task.parent_task_id || task.parent;
            const parentTask = taskMap.get(parentId);
            if (parentTask) {
                parentTask.children.push(currentTask);
            } else {
                // Parent không tồn tại, coi như task cha
                rootTasks.push(currentTask);
            }
        } else {
            // Đây là task cha
            rootTasks.push(currentTask);
        }
    });

    // Sắp xếp tasks: completed tasks xuống dưới
    const sortTasksByCompletion = (tasksList) => {
        return tasksList.sort((a, b) => {
            const aCompleted = a.status === "completed";
            const bCompleted = b.status === "completed";

            if (aCompleted && !bCompleted) return 1;
            if (!aCompleted && bCompleted) return -1;
            return 0;
        });
    };

    // Sắp xếp root tasks
    const sortedRootTasks = sortTasksByCompletion(rootTasks);

    // Sắp xếp children của từng task cha
    sortedRootTasks.forEach((task) => {
        if (task.children && task.children.length > 0) {
            task.children = sortTasksByCompletion(task.children);
        }
    });

    return sortedRootTasks;
};

const useTask = (taskList, boardId, onUpdateTaskList, refreshBoards) => {
    const [editingList, setEditingList] = useState(null);
    const [editingDescription, setEditingDescription] = useState(null);
    const [tasks, setTasks] = useState(taskList.tasks || []);
    const [organizedTasks, setOrganizedTasks] = useState([]);
    const [isAnyModalOpen, setIsAnyModalOpen] = useState(false);
    const { success, error: showError } = useMessage();
    const {
        socket,
        connected,
        emitTaskCreated,
        emitTaskUpdated,
        emitTaskDeleted,
        emitTaskCompleted,
    } = useSocket();

    useEffect(() => {
        setTasks(taskList.tasks || []);
        // Tổ chức tasks thành cấu trúc phân cấp mỗi khi tasks thay đổi
        const organized = organizeTasksHierarchy(taskList.tasks || []);
        setOrganizedTasks(organized);
    }, [taskList.tasks]);

    useEffect(() => {
        if (!socket || !connected) return;

        // Lắng nghe sự kiện từ socket
        socket.on("task:created", (data) => {
            if (
                data.boardId === boardId &&
                data.task.tasklistId === taskList.id
            ) {
                setTasks((prev) => {
                    const newTasks = [...prev, data.task];
                    // Tổ chức lại tasks sau khi thêm mới
                    const organized = organizeTasksHierarchy(newTasks);
                    setOrganizedTasks(organized);
                    return newTasks;
                });
            }
        });

        socket.on("task:updated", (data) => {
            if (
                data.boardId === boardId &&
                data.task.tasklistId === taskList.id
            ) {
                setTasks((prev) => {
                    const updatedTasks = prev.map((task) =>
                        task.id === data.task.id ? data.task : task
                    );
                    // Tổ chức lại tasks sau khi cập nhật
                    const organized = organizeTasksHierarchy(updatedTasks);
                    setOrganizedTasks(organized);
                    return updatedTasks;
                });
            }
        });
        
        socket.on("task:deleted", (data) => {
            if (data.boardId === boardId) {
                setTasks((prev) => {
                    const remainingTasks = prev.filter(
                        (task) => task.id !== data.taskId
                    );
                    // Tổ chức lại tasks sau khi xóa
                    const organized = organizeTasksHierarchy(remainingTasks);
                    setOrganizedTasks(organized);
                    return remainingTasks;
                });
            }
        });

        socket.on("task:completed", (data) => {
            if (data.boardId === boardId && data.task.tasklistId === taskList.id) {
                setTasks((prev) => {
                    const updatedTasks = prev.map((task) =>
                        task.id === data.task.id ? data.task : task
                    );
                    // Tổ chức lại tasks sau khi hoàn thành
                    const organized = organizeTasksHierarchy(updatedTasks);
                    setOrganizedTasks(organized);
                    return updatedTasks;
                });
            }
        });


        return () => {
            socket.off("task:created");
            socket.off("task:updated");
            socket.off("task:deleted");
            socket.off("task:completed");
        };
    }, [socket, connected, boardId, taskList.id]);

    const handleUpdateListTitle = (newTitle) => {
        const currentName = taskList.title || "";
        if (newTitle.trim() !== currentName.trim()) {
            onUpdateTaskList(taskList.id, {
                name: newTitle.trim(),
                title: newTitle.trim(),
            });
        }
        setEditingList(null);
    };

    const handleUpdateDescription = (newDescription) => {
        const currentDescription = taskList.description || "";
        const cleanNewDescription = newDescription.trim();

        if (cleanNewDescription === currentDescription.trim()) {
            setEditingDescription(null);
            return;
        }

        onUpdateTaskList(taskList.id, {
            title: taskList.title,
            description: cleanNewDescription || null,
        });
        setEditingDescription(null);
    };

    const addNewTask = async (listId) => {
        try {
            const newTask = {
                title: "Công việc mới",
                notes: "Thêm mô tả ...",
                priority: "low",
                assignee: "Chưa được gán",
            };

            const response = await TaskService.createTask(
                listId,
                newTask,
                boardId
            );

            // Emit socket event
            emitTaskCreated({
                boardId,
                task: response,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            success("Tạo công việc mới thành công!");
            return response;
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể thêm công việc mới: " + error.message);
                throw new Error("Không thể thêm công việc mới");
            }
        }
    };

    const deleteTask = async (listId, taskId) => {
        try {
            await TaskService.deleteTask(listId, taskId, boardId);

            // Emit socket event
            emitTaskDeleted({
                boardId,
                taskId,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            success("Xóa công việc thành công!");
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể xóa công việc: " + error.message);
                throw new Error("Không thể xóa công việc");
            }
        }
    };

    const updateTask = async (
        listId,
        taskId,
        taskUpdate,
        showMessage = true
    ) => {
        try {
            const response = await TaskService.updateTask(
                listId,
                taskId,
                taskUpdate,
                boardId
            );

            // Emit socket event
            emitTaskUpdated({
                boardId,
                task: response,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            if (showMessage) {
                success("Cập nhật công việc thành công!");
            }
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể cập nhật công việc: " + error.message);
                throw new Error("Không thể cập nhật công việc");
            }
        }
    };

    const addSubtask = async (listId, parentTaskId) => {
        try {
            const newSubtask = {
                title: "Công việc con mới",
                notes: "Thêm mô tả cho công việc con...",
                priority: "medium",
                assignee: "Chưa được gán",
            };

            const response = await TaskService.createSubtask(
                listId,
                parentTaskId,
                newSubtask,
                boardId
            );

            // Emit socket event
            emitTaskCreated({
                boardId,
                task: response,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            success("Tạo công việc con thành công!");
            return response;
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể thêm công việc con: " + error.message);
                throw new Error("Không thể thêm công việc con");
            }
        }
    };

    const toggleTaskComplete = async (
        listId,
        taskId,
        isCompleted,
        childrenTasks = []
    ) => {
        try {
            // Toggle task cha
            const mainTaskResponse = await TaskService.toggleTaskComplete(
                listId,
                taskId,
                isCompleted,
                boardId
            );

            // Emit socket event cho task chính
            emitTaskCompleted({
                boardId,
                task: mainTaskResponse,
            });

            // Nếu có task con, toggle tất cả task con cùng trạng thái
            if (childrenTasks && childrenTasks.length > 0) {
                for (const childTask of childrenTasks) {
                    const childTaskResponse =
                        await TaskService.toggleTaskComplete(
                            listId,
                            childTask.id,
                            isCompleted,
                            boardId
                        );
                    // Emit socket event cho mỗi task con
                    emitTaskCompleted({
                        boardId,
                        task: childTaskResponse,
                    });
                }
            }
            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            const statusText = isCompleted ? "hoàn thành" : "chưa hoàn thành";
            if (childrenTasks && childrenTasks.length > 0) {
                success(
                    `Cập nhật trạng thái công việc cha và ${childrenTasks.length} công việc con thành ${statusText}!`
                );
            } else {
                success(`Cập nhật trạng thái công việc thành ${statusText}!`);
            }
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể cập nhật trạng thái công việc: " + error.message);
                throw new Error("Không thể cập nhật trạng thái công việc");
            }
        }
    };

    return {
        editingList,
        editingDescription,
        tasks,
        organizedTasks,
        isAnyModalOpen,
        setEditingList,
        setEditingDescription,
        setIsAnyModalOpen,
        handleUpdateListTitle,
        handleUpdateDescription,
        addNewTask,
        deleteTask,
        updateTask,
        addSubtask,
        toggleTaskComplete,
    };
};

export default useTask;
