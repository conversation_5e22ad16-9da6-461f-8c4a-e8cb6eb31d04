const { google } = require('googleapis');
const { User } = require('../models');
const dotenv = require('dotenv');
dotenv.config();

const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  `${process.env.BACKEND_URL}/auth/google/callback`
);

// Hàm refresh access token
async function refreshAccessToken(userId) {
  try {
    const user = await User.findByPk(userId);
    if (!user || !user.googleRefreshToken) {
      throw new Error('User không tồn tại hoặc không có refresh token');
    }

    // Gán refresh_token
    oauth2Client.setCredentials({
      refresh_token: user.googleRefreshToken
    });

    // Lấy access token mới
    const accessTokenResponse = await oauth2Client.getAccessToken();
    const newAccessToken = accessTokenResponse.token;

    if (!newAccessToken) {
      throw new Error('Không lấy được access token mới từ Google');
    }

    // Cập nhật access token vào DB
    await user.update({
      googleAccessToken: newAccessToken
    });

    return newAccessToken;
  } catch (error) {
    console.error('Lỗi khi refresh access token:', error);
    throw error;
  }
}


//Hàm lấy Google Tasks API client đã được xác thực
async function getAuthenticatedClient(userId) {
  try {
    const user = await User.findByPk(userId);
    if (!user || !user.googleAccessToken) {
      throw new Error('User không tồn tại hoặc không có access token');
    }

    // Set credentials cho OAuth2 client
    oauth2Client.setCredentials({
      access_token: user.googleAccessToken,
      refresh_token: user.googleRefreshToken
    });

    const tasks = google.tasks({ version: 'v1', auth: oauth2Client });
    return tasks;
  } catch (error) {
    console.error('Lỗi khi lấy authenticated client:', error);
    throw error;
  }
}

//Hàm lấy Google Drive API client đã được xác thực
async function getAuthenticatedDriveClient(userId) {
  try {
    const user = await User.findByPk(userId);
    if (!user || !user.googleAccessToken) {
      throw new Error('User không tồn tại hoặc không có access token');
    }

    // Set credentials cho OAuth2 client
    oauth2Client.setCredentials({
      access_token: user.googleAccessToken,
      refresh_token: user.googleRefreshToken
    });

    return google.drive({ version: 'v3', auth: oauth2Client });
  } catch (error) {
    console.error('Lỗi khi lấy authenticated client:', error);
    throw error;
  }
}

//Hàm Lấy Google Sheet API client đã được xác thực
async function getAuthenticatedSheetClient(userId) {
  try {
    const user = await User.findByPk(userId);
    if (!user || !user.googleAccessToken) {
      throw new Error('User không tồn tại hoặc không có access token');
    }

    // Set credentials cho OAuth2 client
    oauth2Client.setCredentials({
      access_token: user.googleAccessToken,
      refresh_token: user.googleRefreshToken
    });

    const sheets = google.sheets({ version: 'v4', auth: oauth2Client });
    return sheets;
  } catch (error) {
    console.error('Lỗi khi lấy authenticated client:', error);
    throw error;
  }
}

// Middleware để tự động refresh token khi hết hạn
const handleTokenExpiration = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = await User.findByPk(req.user.id);
    if (!user || !user.googleAccessToken) {
      return res.status(401).json({ message: 'Google tokens không tồn tại' });
    }

    // Set credentials
    oauth2Client.setCredentials({
      access_token: user.googleAccessToken,
      refresh_token: user.googleRefreshToken
    });

    try {
      // Thử verify token
      await oauth2Client.getTokenInfo(user.googleAccessToken);
    } catch (error) {
      // Nếu token hết hạn, refresh token
      if (error.message === 'invalid_token') {
        const newAccessToken = await refreshAccessToken(user.id);
        console.log('>> newAccessToken:', newAccessToken);
        user.googleAccessToken = newAccessToken;
      } else {
        throw error;
      }
    }

    next();
  } catch (error) {
    console.error('Lỗi trong middleware handleTokenExpiration:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  refreshAccessToken,
  getAuthenticatedClient,
  getAuthenticatedDriveClient,
  getAuthenticatedSheetClient,
  handleTokenExpiration
}; 