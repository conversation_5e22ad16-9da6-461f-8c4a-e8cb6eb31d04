import { useState, useEffect, useCallback } from 'react';
import WorkspaceService from '../services/workspaceService';
import workspaceAssignService from '../services/workspaceAssignService';
import userService from '../services/userService';
import { useMessage } from '../contexts/MessageContext';
import { 
    getSelectedWorkspace, 
    saveSelectedWorkspace, 
    clearBoardStateForWorkspace 
} from '../utils/stateManager';

const useWorkspace = () => {
    const message = useMessage();
    const [workspaces, setWorkspaces] = useState([]);
    const [selectedWorkspace, setSelectedWorkspace] = useState(() => {
        // Khôi phục workspace từ localStorage khi khởi tạo
        return getSelectedWorkspace();
    });
    const [workspaceLoading, setWorkspaceLoading] = useState(false);
    const [workspaceMembers, setWorkspaceMembers] = useState([]);
    const [isCreatingWorkspace, setIsCreatingWorkspace] = useState(false);
    const [isUpdatingWorkspace, setIsUpdatingWorkspace] = useState(false);
    const [isDeletingWorkspace, setIsDeletingWorkspace] = useState(false);
    const { success, error: showError } = useMessage();

    const fetchWorkspaces = useCallback(async () => {
        try {
            setWorkspaceLoading(true);
            //console.log('Fetching workspaces');

            const currentUser = await userService.getCurrentUser();
            //console.log('Current user:', currentUser);

            const ownedWorkspaces = await WorkspaceService.getWorkspaces();
            //console.log('Owned workspaces:', ownedWorkspaces);

            const assignedWorkspacesResponse = await workspaceAssignService.getUserAssignedWorkspaces(currentUser.id);
            //console.log('Assigned workspaces response:', assignedWorkspacesResponse);

            const assignedWorkspaces = assignedWorkspacesResponse.map(item => ({
                ...item.workspace,
                isAssigned: true
            }));

            const ownedWorkspaceIds = ownedWorkspaces.map(ws => ws.id);
            const uniqueAssignedWorkspaces = assignedWorkspaces.filter(ws => !ownedWorkspaceIds.includes(ws.id));

            const allWorkspaces = [
                ...ownedWorkspaces.map(ws => ({ ...ws, isOwned: true })),
                ...uniqueAssignedWorkspaces
            ];

            //console.log('All workspaces combined:', allWorkspaces);
            setWorkspaces(allWorkspaces);

            // Khôi phục workspace từ localStorage hoặc chọn workspace đầu tiên
            if (allWorkspaces.length > 0) {
                const savedWorkspaceId = getSelectedWorkspace();
                const workspaceExists = savedWorkspaceId && allWorkspaces.find(ws => ws.id === savedWorkspaceId);
                
                if (workspaceExists) {
                    setSelectedWorkspace(savedWorkspaceId);
                } else {
                    // Luôn chọn workspace đầu tiên nếu không có workspace được lưu hoặc workspace đã lưu không tồn tại
                    setSelectedWorkspace(allWorkspaces[0].id);
                    saveSelectedWorkspace(allWorkspaces[0].id);
                }
            }
        } catch (error) {
            //console.error('Lỗi khi tải danh sách không gian làm việc:', error);
            showError('Không thể tải danh sách không gian làm việc');
        } finally {
            setWorkspaceLoading(false);
        }
    }, [selectedWorkspace, message]);

    const createWorkspace = useCallback(async (workspaceData) => {
        try {
            setIsCreatingWorkspace(true);
            const response = await WorkspaceService.createWorkspace(workspaceData);
            setWorkspaces(prevWorkspaces => [...prevWorkspaces, response]);
            setSelectedWorkspace(response.id);
            saveSelectedWorkspace(response.id);
            success('Tạo không gian làm việc thành công!');
            return response;
        } catch (error) {
            console.error('Lỗi khi tạo không gian làm việc:', error);
            showError('Không thể tạo không gian làm việc: ' + (error.response?.data?.message || error.message));
            throw error;
        } finally {
            setIsCreatingWorkspace(false);
        }
    }, [message]);

    const updateWorkspace = useCallback(async (workspaceId, updateData) => {
        try {
            setIsUpdatingWorkspace(true);
            const response = await WorkspaceService.updateWorkspace(workspaceId, updateData);
            setWorkspaces(prevWorkspaces => 
                prevWorkspaces.map(ws => ws.id === workspaceId ? { ...ws, ...response } : ws)
            );
            success('Cập nhật không gian làm việc thành công!');
            return response;
        } catch (error) {
            console.error('Lỗi khi cập nhật không gian làm việc:', error);
            showError('Không thể cập nhật không gian làm việc: ' + (error.response?.data?.message || error.message));
            throw error;
        } finally {
            setIsUpdatingWorkspace(false);
        }
    }, [message]);

    const deleteWorkspace = useCallback(async (workspaceId) => {
        try {
            setIsDeletingWorkspace(true);

            // Kiểm tra xem có phải workspace mặc định không
            const isDefaultWorkspace = workspaces.find(
                (workspace) => workspace.id === workspaceId && workspace.isDefault
            );
            if (isDefaultWorkspace) {
                showError("Không thể xóa không gian làm việc mặc định");
                return;
            }

            await WorkspaceService.deleteWorkspace(workspaceId);
            
            setWorkspaces(prevWorkspaces => {
                const updatedWorkspaces = prevWorkspaces.filter(ws => ws.id !== workspaceId);
                if (selectedWorkspace === workspaceId && updatedWorkspaces.length > 0) {
                    const newWorkspaceId = updatedWorkspaces[0].id;
                    setSelectedWorkspace(newWorkspaceId);
                    saveSelectedWorkspace(newWorkspaceId);
                } else if (updatedWorkspaces.length === 0) {
                    setSelectedWorkspace(null);
                    saveSelectedWorkspace(null);
                }
                return updatedWorkspaces;
            });
            
            // Xóa board state cho workspace đã xóa
            clearBoardStateForWorkspace(workspaceId);
            
            success('Xóa không gian làm việc thành công!');
        } catch (error) {
            console.error('Lỗi khi xóa không gian làm việc:', error);
            showError('Không thể xóa không gian làm việc: ' + (error.response?.data?.message || error.message));
            throw error;
        } finally {
            setIsDeletingWorkspace(false);
        }
    }, [selectedWorkspace, message]);

    const fetchWorkspaceMembers = useCallback(async (workspaceId) => {
        if (!workspaceId) return;

        try {
            const members = await workspaceAssignService.getWorkspaceAssignees(workspaceId);
            setWorkspaceMembers(members || []);
        } catch (error) {
            console.error('Lỗi khi tải danh sách thành viên không gian làm việc:', error);
            setWorkspaceMembers([]);
            showError('Không thể tải danh sách thành viên không gian làm việc');
        }
    }, [message]);

    // Wrapper function để cập nhật selectedWorkspace và lưu vào localStorage
    const updateSelectedWorkspace = useCallback((workspaceId) => {
        setSelectedWorkspace(workspaceId);
        saveSelectedWorkspace(workspaceId);
    }, []);

    useEffect(() => {
        fetchWorkspaces();
    }, [fetchWorkspaces]);

    return {
        workspaces,
        selectedWorkspace,
        workspaceLoading,
        workspaceMembers,
        isCreatingWorkspace,
        isUpdatingWorkspace,
        isDeletingWorkspace,
        setSelectedWorkspace: updateSelectedWorkspace,
        fetchWorkspaces,
        createWorkspace,
        updateWorkspace,
        deleteWorkspace,
        fetchWorkspaceMembers
    };
};

export default useWorkspace; 