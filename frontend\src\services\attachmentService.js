import axios from '../utils/axiosCustomize';

const uploadAttachment = async (taskId, formData) => {
    const response = await axios.post(`/task-attachments/${taskId}`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
    return response.data;
}

const deleteAttachment = async (taskId, fileId) => {
    const response = await axios.delete(`/task-attachments/${taskId}/${fileId}`);
    return response.data;
}

const getAttachments = async (taskId) => {
    const response = await axios.get(`/task-attachments/${taskId}`);
    return response.data;
}

export default {
    uploadAttachment,
    deleteAttachment,
    getAttachments,
}