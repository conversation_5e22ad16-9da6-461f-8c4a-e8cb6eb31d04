const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
const { User, TaskList, Task, Board, Workspace } = require("../models");
const dotenv = require("dotenv");
dotenv.config();
const { getAuthenticatedClient } = require("../middleware/googleAuth");
const { v4: uuidv4 } = require('uuid');
const { getNextPosition, getMiddlePosition } = require("../utils/position");

passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: `${process.env.BACKEND_URL}/api/auth/google/callback`,
      accessType: 'offline',     
      prompt: 'select_account', 
      include_granted_scopes: true
    },
    async function(accessToken, refreshToken, profile, done) {
      try {
        const [user, created] = await User.findOrCreate({
          where: { googleUserId: profile.id },
          defaults: {
            email: profile.emails[0].value,
            fullName: profile.displayName,
            photoUrl: profile.photos[0].value,
            googleAccessToken: accessToken,
            googleRefreshToken: refreshToken
          }
        });

        if (!created) {
          // Cập nhật tokens mới nếu user đã tồn tại
          await user.update({
            googleAccessToken: accessToken,
            googleRefreshToken: refreshToken
          });
        } else {
          // Nếu là user mới, tạo workspace mặc định trước
          const defaultWorkspace = await Workspace.create({
            name: profile.displayName,
            description: 'Mặc định',
            ownerId: user.id,
            isDefault: true
          });
        
          // Sau đó tạo board mặc định "Main Board"
          const mainBoard = await Board.create({
            name: 'Main Board',
            createdBy: user.id,
            workspaceId: defaultWorkspace.id,
            isGoogleSynced: true,
            isDefault: true
          });
        
          // Đồng bộ với Google Tasks
          const tasks = await getAuthenticatedClient(user.id);
        
          // Lấy tất cả Google TaskLists (paging bằng nextPageToken)
          let taskLists = [];
          let taskListPageToken = null;
        
          do {
            const response = await tasks.tasklists.list({
              maxResults: 100,
              pageToken: taskListPageToken
            });
        
            taskLists.push(...(response.data.items || []));
            taskListPageToken = response.data.nextPageToken;
          } while (taskListPageToken);

          // Khởi tạo position cho TaskList đầu tiên
          let currentTaskListRank = getNextPosition();
        
          // Lặp qua từng taskList
          for (let i = 0; i < taskLists.length; i++) {
            const taskList = taskLists[i];
            const taskListID = uuidv4();
            await TaskList.upsert({
              id: taskListID,
              title: taskList.title,
              googleTaskListId: taskList.id,
              updatedAt: taskList.updated,
              boardId: mainBoard.id,
              ownerId: user.id,
              position: currentTaskListRank,
              isDefault: i === 0 // Đặt tasklist đầu tiên làm mặc định
            });

            // Cập nhật position cho TaskList tiếp theo
            currentTaskListRank = getNextPosition(currentTaskListRank);
        
            // Lấy toàn bộ task trong taskList (paging)
            let tasksData = [];
            let taskPageToken = null;
        
            do {
              const taskResponse = await tasks.tasks.list({
                tasklist: taskList.id,
                showCompleted: true,
                showHidden: true,
                maxResults: 100,
                pageToken: taskPageToken
              });
        
              tasksData.push(...(taskResponse.data.items || []));
              taskPageToken = taskResponse.data.nextPageToken;
            } while (taskPageToken);
        
            // Tạo từng task
            for (const task of tasksData) {
              await Task.upsert({
                id: uuidv4(),
                tasklist_id: taskListID,
                googleTaskId: task.id,
                title: task.title,
                notes: task.notes,
                due: task.due,
                position: task.position,
                status: task.status,
                hidden: task.hidden,
                updated_at: task.updated,
                completed: task.completed,
                created_by: user.id,
                parent_task_id: task.parent || null,
                priority: 'low',
              });
            }
          }
        }
        
        return done(null, user);
      } catch (error) {
        return done(error, null);
      }
    }
  )
);

passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  const user = await User.findByPk(id);
  done(null, user);
});

module.exports = passport; 