import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

const NotFound = () => {
    const navigate = useNavigate();

    return (
        <Result
            status='404'
            title='404'
            subTitle='Xin lỗi, trang bạn tìm kiếm không tồn tại.'
            extra={
                <div
                    style={{
                        display: 'flex',
                        gap: '12px',
                        justifyContent: 'center'
                    }}
                >
                    <Button
                        type='primary'
                        onClick={() => navigate('/app/dashboard')}
                    >
                        Về Dashboard
                    </Button>
                    <Button onClick={() => navigate('/')}>Về Trang chủ</Button>
                </div>
            }
        />
    );
};

export default NotFound;
