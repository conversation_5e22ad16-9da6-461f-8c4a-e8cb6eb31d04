const workspaceAssignService = require('../services/workspaceAssignService');
const { WorkspaceAssignee } = require('../models');

const assignUserToWorkspace = async (req, res) => {
    const { userId, workspaceId } = req.body;
    try {
        const workspaceAssignee = await workspaceAssignService.assignUserToWorkspace(userId, workspaceId);
        res.status(200).json(workspaceAssignee);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const unassignUserFromWorkspace = async (req, res) => {
    const { userId, workspaceId } = req.body;
    try {
        await workspaceAssignService.unassignUserFromWorkspace(userId, workspaceId);
        
        res.status(200).json({ 
            message: 'User successfully removed from workspace and all related boards and tasks',
            userId,
            workspaceId
        });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const getWorkspaceAssignees = async (req, res) => {
    const { workspaceId } = req.params;
    try {
        const workspaceAssignees = await workspaceAssignService.getWorkspaceAssignees(workspaceId);
        res.status(200).json(workspaceAssignees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const getUserAssignedWorkspaces = async (req, res) => {
    const { userId } = req.params;
    try {
        const userAssignedWorkspaces = await workspaceAssignService.getUserAssignedWorkspaces(userId);
        res.status(200).json(userAssignedWorkspaces);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

module.exports = { assignUserToWorkspace, unassignUserFromWorkspace, getWorkspaceAssignees, getUserAssignedWorkspaces };