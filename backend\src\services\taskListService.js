const { TaskList, Board, Task } = require("../models");
const { v4: uuidv4 } = require("uuid");
const { Op } = require("sequelize");
const { getNextPosition, getMiddlePosition } = require("../utils/position");

class TaskListService {
    getNewTaskListPosition(taskLists) {
        try {
            if (!taskLists || taskLists.length === 0)
                return getNextPosition("0");

            const lastTaskList = taskLists[taskLists.length - 1];
            const lastPosition = lastTaskList?.position;

            if (!lastPosition) {
                console.warn(
                    "TaskList cuối không có position, trả về position mới"
                );
                return getNextPosition("0");
            }

            return getNextPosition(lastPosition);
        } catch (error) {
            console.error("Lỗi khi tạo position mới:", error);
            throw new Error("Lỗi khi tạo position mới");
        }
    }

    async getTaskListById(taskListId) {
        return await TaskList.findByPk(taskListId);
    }

    async getBoardForUser(boardId, userId) {
        return await Board.findOne({
            where: {
                id: boardId,
                createdBy: userId,
            },
        });
    }

    async getTaskLists(boardId, userId) {
        return await TaskList.findAll({
            where: {
                ownerId: userId,
                boardId: boardId,
            },
            order: [["position", "ASC"]],
        });
    }

    async getDefaultBoard(userId) {
        return await Board.findOne({
            where: {
                name: "Main Board",
                createdBy: userId,
            },
        });
    }

    async getAllTaskLists(userId) {
        return await TaskList.findAll({
            where: { ownerId: userId },
        });
    }

    async createTaskList(boardId, userId, taskListData) {
        const { title, description, color } = taskListData;

        const existingTaskLists = await TaskList.findAll({
            where: { boardId: boardId },
            order: [["position", "ASC"]],
        });

        const newPosition = this.getNewTaskListPosition(existingTaskLists);

        const newTaskList = await TaskList.create({
            id: uuidv4(),
            title: title,
            description: description || null,
            ownerId: userId,
            boardId: boardId,
            position: newPosition,
        });

        return {
            id: newTaskList.id,
            title: newTaskList.title,
            description: newTaskList.description,
            name: newTaskList.title,
            color: color || "#1890ff",
            tasks: [],
            ownerId: newTaskList.ownerId,
            boardId: newTaskList.boardId,
            position: newTaskList.position,
            createdAt: newTaskList.createdAt,
            updatedAt: newTaskList.updatedAt,
        };
    }

    async updateTaskList(taskListId, updateData) {
        const { title, description } = updateData;

        await TaskList.update(
            {
                title: title,
                description: description,
                updated_at: new Date(),
            },
            { where: { id: taskListId } }
        );

        return await TaskList.findByPk(taskListId);
    }

    async deleteTaskList(taskListId) {
        const taskList = await TaskList.findByPk(taskListId);
        if (!taskList) {
            throw new Error("TaskList không tồn tại");
        }

        const relatedTasks = await Task.findAll({
            where: { tasklist_id: taskListId },
        });

        for (const task of relatedTasks) {
            await Task.destroy({
                where: { id: task.id },
            });
        }

        await TaskList.destroy({
            where: { id: taskListId },
        });
    }

    async getTaskListByBoardId(boardId) {
        return await TaskList.findAll({
            where: { boardId },
            include: [
                {
                    association: "tasks",
                    required: false,
                    separate: true,
                    order: [["position", "ASC"]],
                },
            ],
            order: [["position", "ASC"]],
        });
    }

    async updateTaskListsOrder(boardId, taskListIds) {
        for (const [index, taskListId] of taskListIds.entries()) {
            await TaskList.update(
                {
                    position: index,
                },
                {
                    where: { id: taskListId },
                }
            );
        }
    }

    async markBoardForSync(boardId) {
        await Board.update(
            {
                isGoogleSynced: false,
            },
            {
                where: { id: boardId },
            }
        );
    }

    async moveTaskList(taskListId, previousTaskListId = null, nextTaskListId = null) {
        // Lấy task list hiện tại
        const taskList = await TaskList.findByPk(taskListId);
        if (!taskList) {
            throw new Error("TaskList không tồn tại");
        }

        // Tính toán position mới
        let newPositionValue;
        
        if (!previousTaskListId && !nextTaskListId) {
            // Nếu không có task list trước và sau, đặt ở đầu danh sách
            newPositionValue = getNextPosition("0");
        } else if (previousTaskListId && !nextTaskListId) {
            // Nếu chỉ có task list trước, đặt ở cuối
            const previousTaskList = await TaskList.findByPk(previousTaskListId);
            
            if (previousTaskList) {
                // Tính position mới dựa trên position của task list trước đó
                newPositionValue = getNextPosition(previousTaskList.position);
            } else {
                // Nếu không tìm thấy task list trước đó, đặt ở đầu
                newPositionValue = getNextPosition("0");
            }
        } else if (!previousTaskListId && nextTaskListId) {
            // Nếu chỉ có task list sau, đặt ở đầu
            const nextTaskList = await TaskList.findByPk(nextTaskListId);
            
            if (nextTaskList) {
                // Tính position mới dựa trên position của task list sau đó
                newPositionValue = getMiddlePosition("0", nextTaskList.position);
            } else {
                // Nếu không tìm thấy task list sau đó, đặt ở đầu
                newPositionValue = getNextPosition("0");
            }
        } else {
            // Có cả task list trước và sau - đặt ở giữa
            const previousTaskList = await TaskList.findByPk(previousTaskListId);
            const nextTaskList = await TaskList.findByPk(nextTaskListId);
            
            if (previousTaskList && nextTaskList) {
                // Tính position mới ở giữa hai task list
                newPositionValue = getMiddlePosition(previousTaskList.position, nextTaskList.position);
            } else if (previousTaskList) {
                // Chỉ có task list trước
                newPositionValue = getNextPosition(previousTaskList.position);
            } else if (nextTaskList) {
                // Chỉ có task list sau
                newPositionValue = getMiddlePosition("0", nextTaskList.position);
            } else {
                // Không có task list nào, đặt ở đầu
                newPositionValue = getNextPosition("0");
            }
        }

        // Cập nhật position của task list
        await TaskList.update(
            {
                position: newPositionValue,
            },
            {
                where: { id: taskListId },
            }
        );

        return await TaskList.findByPk(taskListId);
    }
 
}

module.exports = new TaskListService();
