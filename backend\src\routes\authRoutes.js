const express = require('express');
const passport = require('passport');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const {
  getLoginPage,
  handleGoogleCallback,
  getProfile,
  logout
} = require('../controllers/authController');

//login
router.get('/', getLoginPage);

//login with google
router.get('/google',
  passport.authenticate('google', { 
    scope: [
      'profile', 
      'email',
      'https://www.googleapis.com/auth/tasks',
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/spreadsheets"
    ],
    accessType: 'offline',     
    prompt: 'select_account',
    include_granted_scopes: true         
  })
);

//callback from google
router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/' }),
  handleGoogleCallback
);

router.get('/profile', isAuthenticated, getProfile);

//logout
router.get('/logout', isAuthenticated, logout);

module.exports = router; 