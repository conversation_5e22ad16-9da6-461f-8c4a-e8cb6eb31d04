const { google } = require('googleapis');
const { User } = require('../models');
const dotenv = require('dotenv');
dotenv.config();

const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  `${process.env.BACKEND_URL}/auth/google/callback`
);

const isAuthenticated = async (req, res, next) => {
  try {
    if (req.isAuthenticated()) return next();

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Unauthorized - No valid session or token provided' });
    }

    const accessToken = authHeader.substring(7);
    oauth2Client.setCredentials({ access_token: accessToken });

    try {
      const tokenInfo = await oauth2Client.getTokenInfo(accessToken);

      if (!tokenInfo || !tokenInfo.aud) {
        return res.status(401).json({ message: 'Invalid access token' });
      }

      const user = await User.findOne({ where: { googleAccessToken: accessToken } });
      if (!user) {
        return res.status(401).json({ message: 'User not found for this access token' });
      }

      req.user = user;
      return next();
    } catch (tokenError) {
      console.error('Token verification failed:', tokenError);

      if (
        tokenError.code === 'ERR_BAD_REQUEST' ||
        tokenError.message.includes('invalid') ||
        tokenError.response?.status === 401
      ) {
        try {
          const user = await User.findOne({ where: { googleAccessToken: accessToken } });

          if (user && user.googleRefreshToken) {
            oauth2Client.setCredentials({ refresh_token: user.googleRefreshToken });

            const accessTokenResponse = await oauth2Client.getAccessToken();
            const newAccessToken = accessTokenResponse.token;

            if (!newAccessToken) {
              throw new Error('Failed to refresh access token');
            }

            await user.update({ googleAccessToken: newAccessToken });
            req.user = user;

            res.setHeader('X-New-Access-Token', newAccessToken);
            return next();
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
        }
      }

      return res.status(401).json({ message: 'Token expired or invalid', code: 'TOKEN_EXPIRED' });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ message: 'Internal server error during authentication' });
  }
};


module.exports = { isAuthenticated };