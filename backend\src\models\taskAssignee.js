module.exports = (sequelize, DataTypes) => {
    const TaskAssignee = sequelize.define('TaskAssignee', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        taskId: {
            type: DataTypes.STRING(255),
            allowNull: false,
            field: 'task_id'
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            field: 'user_id'
        },
        fullName: {
            type: DataTypes.STRING(255),
            allowNull: false,
            field: 'full_name'
        },
        email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            field: 'email'
        },
        assignedAt: {
            type: DataTypes.DATE,
            field: 'assigned_at',
            defaultValue: DataTypes.NOW
        }
    }, {
        tableName: 'task_assignees',
        timestamps: false
    });

    TaskAssignee.associate = (models) => {
        TaskAssignee.belongsTo(models.Task, {
            foreignKey: 'taskId', 
            as: 'task',
            onDelete: 'CASCADE'
        });
        TaskAssignee.belongsTo(models.User, {
            foreignKey: 'userId', 
            as: 'user',
            onDelete: 'CASCADE'
        });
    };

    return TaskAssignee;
};
