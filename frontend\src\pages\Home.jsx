import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from '../utils/axiosCustomize';
import { setToken, getToken } from '../utils/tokenManager';
import './Home.css';
import { GoogleOutlined } from '@ant-design/icons';

const Home = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [message, setMessage] = useState('');
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        // Xử lý query parameters từ OAuth callback
        const urlParams = new URLSearchParams(location.search);
        const token = urlParams.get('token');
        const error = urlParams.get('error');

        if (token) {
            // Lưu token vào localStorage
            setToken(token);
            setMessage('Đăng nhập thành công!');

            // Xóa query parameters khỏi URL
            window.history.replaceState(
                {},
                document.title,
                window.location.pathname
            );
            setMessage('');

            navigate('/app/dashboard');
        } else if (error) {
            let errorMessage = 'Đăng nhập thất bại';
            if (error === 'login_failed') {
                errorMessage = 'Đăng nhập không thành công. Vui lòng thử lại.';
            } else if (error === 'callback_error') {
                errorMessage =
                    'Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại.';
            }
            setMessage(errorMessage);

            // Xóa query parameters khỏi URL
            window.history.replaceState(
                {},
                document.title,
                window.location.pathname
            );

            setMessage('');
        }
    }, [location.search, navigate]);

    const handleLogin = async (e) => {
        try {
            setIsLoading(true);

            // Kiểm tra token hiện tại trước khi đăng nhập
            const existingToken = getToken();
            if (existingToken) {
                try {
                    const response = await axios.get('/auth/profile');
                    if (response.data.status === 'success') {
                        // Token hợp lệ, chuyển thẳng đến dashboard
                        // console.log('Token đã hợp lệ, chuyển đến dashboard');
                        navigate('/app/dashboard');
                        return;
                    }
                } catch (error) {
                    // Token không hợp lệ, tiếp tục đăng nhập Google
                    // console.log(
                    //     'Token không hợp lệ, tiếp tục đăng nhập Google'
                    // );
                }
            }

            // Chuyển đến Google OAuth nếu chưa có token hoặc token không hợp lệ
            window.location.href = `${
                import.meta.env.VITE_API_URL
            }/auth/google`;
        } catch (error) {
            const errorMessage = error.response?.data?.error || 'Login failed';
            setMessage(errorMessage);
            setIsLoading(false);
        }
    };

    return (
        <div className='home-container'>
            {/* Notification Message */}
            {message && (
                <div
                    className={`notification ${
                        message.includes('thành công') ? 'success' : 'error'
                    }`}
                >
                    {message}
                </div>
            )}

            {/* Loading Overlay */}
            {isLoading && (
                <div className='loading-overlay'>
                    <div className='loading-spinner'>Đang chuyển hướng...</div>
                </div>
            )}

            {/* Header */}
            <header className='header'>
                <div className='container'>
                    <div className='header-content'>
                        <div className='logo'>
                            <img
                                src={'/gwtask.png'}
                                style={{ width: '24px', height: '24px' }}
                            />
                            <span className='logo-text'>GW Tasks</span>
                        </div>

                        <nav className='nav-menu'>
                            {/* <a href='#pricing'>Pricing</a>
                            <a href='#features'>Features</a> */}
                            <a onClick={handleLogin} className='signin-btn'>
                                Đăng nhập
                            </a>
                        </nav>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <section className='hero'>
                <div className='container'>
                    <div className='hero-content'>
                        <div className='hero-text'>
                            <h1>GW Tasks</h1>
                            <h2>Ứng dụng desktop cho Google Tasks</h2>
                            <button
                                className='cta-button'
                                onClick={handleLogin}
                                disabled={isLoading}
                            >
                                <svg
                                    width='20'
                                    height='20'
                                    viewBox='0 0 24 24'
                                    fill='none'
                                    xmlns='http://www.w3.org/2000/svg'
                                >
                                    <path
                                        fill='#4285F4'
                                        d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'
                                    />
                                    <path
                                        fill='#34A853'
                                        d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'
                                    />
                                    <path
                                        fill='#FBBC05'
                                        d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'
                                    />
                                    <path
                                        fill='#EA4335'
                                        d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'
                                    />
                                </svg>
                                {isLoading
                                    ? 'Đang xử lý...'
                                    : 'Đăng nhập với Google'}
                            </button>
                            <p className='hero-description'>
                                Quản lý Google Tasks của bạn trên ứng dụng toàn
                                màn hình
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className='features' id='features'>
                <div className='container'>
                    {/* Feature 1 */}
                    <div className='feature-section'>
                        <div className='feature-content'>
                            <h3>
                                Hoàn thành nhiều việc hơn trên toàn màn hình
                            </h3>
                            <h4>
                                Sắp xếp danh sách của bạn trong bảng toàn màn
                                hình để luôn kiểm soát công việc.
                            </h4>
                            <button
                                className='feature-cta'
                                onClick={handleLogin}
                                disabled={isLoading}
                            >
                                {isLoading ? 'Đang xử lý...' : 'Bắt đầu'}
                            </button>
                        </div>
                        <div className='feature-image'>
                            <div className='feature-placeholder'>
                                <svg
                                    width='400'
                                    height='300'
                                    viewBox='0 0 400 300'
                                    fill='none'
                                >
                                    <rect
                                        width='400'
                                        height='300'
                                        rx='12'
                                        fill='#f8fafc'
                                    />
                                    <rect
                                        x='20'
                                        y='20'
                                        width='360'
                                        height='40'
                                        rx='8'
                                        fill='#e2e8f0'
                                    />
                                    <rect
                                        x='20'
                                        y='80'
                                        width='110'
                                        height='200'
                                        rx='8'
                                        fill='#ffffff'
                                        stroke='#e2e8f0'
                                    />
                                    <rect
                                        x='145'
                                        y='80'
                                        width='110'
                                        height='200'
                                        rx='8'
                                        fill='#ffffff'
                                        stroke='#e2e8f0'
                                    />
                                    <rect
                                        x='270'
                                        y='80'
                                        width='110'
                                        height='200'
                                        rx='8'
                                        fill='#ffffff'
                                        stroke='#e2e8f0'
                                    />
                                    <text
                                        x='75'
                                        y='105'
                                        textAnchor='middle'
                                        fill='#64748b'
                                        fontSize='12'
                                    >
                                        To Do
                                    </text>
                                    <text
                                        x='200'
                                        y='105'
                                        textAnchor='middle'
                                        fill='#64748b'
                                        fontSize='12'
                                    >
                                        In Progress
                                    </text>
                                    <text
                                        x='325'
                                        y='105'
                                        textAnchor='middle'
                                        fill='#64748b'
                                        fontSize='12'
                                    >
                                        Done
                                    </text>
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Feature 2 */}
                    <div className='feature-section reverse'>
                        <div className='feature-content'>
                            <h3>Chia sẻ tasks của bạn</h3>
                            <h4>
                                Chia sẻ danh sách Google Tasks của bạn theo thời
                                gian thực với team bằng một liên kết.
                            </h4>
                            <button
                                className='feature-cta'
                                onClick={handleLogin}
                                disabled={isLoading}
                            >
                                {isLoading ? 'Đang xử lý...' : 'Bắt đầu'}
                            </button>
                        </div>
                        <div className='feature-image'>
                            <div className='feature-placeholder'>
                                <svg
                                    width='400'
                                    height='300'
                                    viewBox='0 0 400 300'
                                    fill='none'
                                >
                                    <rect
                                        width='400'
                                        height='300'
                                        rx='12'
                                        fill='#f8fafc'
                                    />
                                    <circle
                                        cx='200'
                                        cy='150'
                                        r='80'
                                        fill='#667eea'
                                        opacity='0.1'
                                    />
                                    <circle
                                        cx='120'
                                        cy='120'
                                        r='30'
                                        fill='#667eea'
                                    />
                                    <circle
                                        cx='280'
                                        cy='120'
                                        r='30'
                                        fill='#764ba2'
                                    />
                                    <circle
                                        cx='120'
                                        cy='200'
                                        r='30'
                                        fill='#34d399'
                                    />
                                    <circle
                                        cx='280'
                                        cy='200'
                                        r='30'
                                        fill='#f59e0b'
                                    />
                                    <path
                                        d='M150 120L200 150M200 150L250 120M200 150L150 200M200 150L250 200'
                                        stroke='#667eea'
                                        strokeWidth='2'
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Feature 3 */}
                    <div className='feature-section'>
                        <div className='feature-content'>
                            <h3>Tích hợp hoàn toàn với Google Workspace</h3>
                            <h4>
                                Xuất danh sách sang Google Sheets, đính kèm file
                                Google Drive và email Gmail vào tasks.
                            </h4>
                            <button
                                className='feature-cta'
                                onClick={handleLogin}
                                disabled={isLoading}
                            >
                                {isLoading ? 'Đang xử lý...' : 'Bắt đầu'}
                            </button>
                        </div>
                        <div className='feature-image'>
                            <div className='feature-placeholder'>
                                <svg
                                    width='400'
                                    height='300'
                                    viewBox='0 0 400 300'
                                    fill='none'
                                >
                                    <rect
                                        width='400'
                                        height='300'
                                        rx='12'
                                        fill='#f8fafc'
                                    />
                                    <rect
                                        x='50'
                                        y='50'
                                        width='80'
                                        height='80'
                                        rx='8'
                                        fill='#4285f4'
                                    />
                                    <rect
                                        x='160'
                                        y='50'
                                        width='80'
                                        height='80'
                                        rx='8'
                                        fill='#34a853'
                                    />
                                    <rect
                                        x='270'
                                        y='50'
                                        width='80'
                                        height='80'
                                        rx='8'
                                        fill='#fbbc04'
                                    />
                                    <rect
                                        x='50'
                                        y='160'
                                        width='80'
                                        height='80'
                                        rx='8'
                                        fill='#ea4335'
                                    />
                                    <rect
                                        x='160'
                                        y='160'
                                        width='80'
                                        height='80'
                                        rx='8'
                                        fill='#667eea'
                                    />
                                    <rect
                                        x='270'
                                        y='160'
                                        width='80'
                                        height='80'
                                        rx='8'
                                        fill='#9333ea'
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Feature 4 */}
                    <div className='feature-section reverse'>
                        <div className='feature-content'>
                            <h3>Đồng bộ trên tất cả thiết bị</h3>
                            <h4>
                                GW Tasks luôn đồng bộ với Google Tasks trên
                                Gmail, Calendar và Google Tasks mobile.
                            </h4>
                            <button
                                className='feature-cta'
                                onClick={handleLogin}
                                disabled={isLoading}
                            >
                                {isLoading ? 'Đang xử lý...' : 'Bắt đầu'}
                            </button>
                        </div>
                        <div className='feature-image'>
                            <div className='feature-placeholder'>
                                <svg
                                    width='400'
                                    height='300'
                                    viewBox='0 0 400 300'
                                    fill='none'
                                >
                                    <rect
                                        width='400'
                                        height='300'
                                        rx='12'
                                        fill='#f8fafc'
                                    />
                                    <rect
                                        x='50'
                                        y='80'
                                        width='80'
                                        height='140'
                                        rx='12'
                                        fill='#667eea'
                                    />
                                    <rect
                                        x='160'
                                        y='50'
                                        width='80'
                                        height='120'
                                        rx='8'
                                        fill='#764ba2'
                                    />
                                    <rect
                                        x='270'
                                        y='100'
                                        width='80'
                                        height='100'
                                        rx='6'
                                        fill='#34d399'
                                    />
                                    <text
                                        x='90'
                                        y='250'
                                        textAnchor='middle'
                                        fill='#64748b'
                                        fontSize='12'
                                    >
                                        iOS
                                    </text>
                                    <text
                                        x='200'
                                        y='220'
                                        textAnchor='middle'
                                        fill='#64748b'
                                        fontSize='12'
                                    >
                                        Desktop
                                    </text>
                                    <text
                                        x='310'
                                        y='240'
                                        textAnchor='middle'
                                        fill='#64748b'
                                        fontSize='12'
                                    >
                                        Android
                                    </text>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className='footer'>
                <div className='container'>
                    <div className='footer-content'>
                        <div className='footer-links'>
                            <a href='#privacy'>GW Tasks</a>
                            <a href='#privacy'>Quyền riêng tư</a>
                            <a href='#terms'>Điều khoản</a>
                            <a href='#help'>Trợ giúp</a>
                        </div>
                        <div className='footer-contact'>
                            <a href='#company'>Công ty</a>
                            <a href='#contact'>Liên hệ</a>
                        </div>
                    </div>
                    <div className='footer-bottom'>
                        <p>
                            GW Tasks không liên kết với Google dưới bất kỳ hình
                            thức nào.
                        </p>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default Home;
