const {getAuthenticatedClient} = require('../middleware/googleAuth');
const {Task, TaskList, Board, sequelize} = require('../models');
const {retryGoogleCall} = require('../utils/googleRetry');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const { formatDateForGoogleTasks } = require('../utils/date');

class AsyncService {
    static async syncTaskListToGoogle(taskListId, userId, forceUpdate = false) { 
        try {
            const taskList = await TaskList.findByPk(taskListId);
            if (!taskList) throw new Error('TaskList không tồn tại');

            const googleTasks = await getAuthenticatedClient(userId);
            let googleTaskList;

            if (taskList.googleTaskListId) {
                try {
                    //console.log('>> update tasklist:', taskList.googleTaskListId);
                    //Kiểm tra tasklist tồn tại trước khi update
                    const taskListExists = await googleTasks.tasklists.get({
                        tasklist: taskList.googleTaskListId
                    });
                    if (!taskListExists) {
                        console.warn(`Google TaskList không tồn tại. Xoá googleTaskListId.`);
                        await TaskList.destroy({ where: { id: taskListId } });
                        return null;
                    }
    
                    //Nếu tồn tại → update
                    googleTaskList = await retryGoogleCall(
                        () => googleTasks.tasklists.update({
                            tasklist: taskList.googleTaskListId,
                            requestBody: {
                                id: taskList.googleTaskListId,
                                title: taskList.title 
                            }
                        }),
                        `update task list "${taskList.title}"`,
                        { taskListId: taskList.id, title: taskList.title, userId }
                    );
    
                } catch (error) {
                    if (error.response?.status === 404) {
                        console.warn(`Google TaskList không tồn tại. Xoá googleTaskListId.`);
    
                        await TaskList.destroy({ where: { id: taskListId } });
    
                        return null;
                    } else {
                        throw error;
                    }
                }
            } else {
                //console.log('>> insert tasklist:', taskList.title);
                googleTaskList = await retryGoogleCall(
                    () => googleTasks.tasklists.insert({
                        requestBody: { title: taskList.title }
                    }),
                    `insert task list "${taskList.title}"`,
                    { taskListId: taskList.id, title: taskList.title, userId }
                );
                await TaskList.update(
                    { googleTaskListId: googleTaskList.data.id },
                    { where: { id: taskListId } }
                );
            }

            return googleTaskList.data;
        } catch (error) {
            console.error('Lỗi khi đồng bộ task list:', error);
            throw error;
        }
    }

    static async syncTaskToGoogle(taskId, userId, forceUpdate = false) {
        try {
            //lấy thông tin task và tasklist
            const task = await Task.findByPk(taskId, {
                include: [{ model: TaskList, as: 'taskList' }]
            });
            if (!task) throw new Error('Task không tồn tại');

            //nếu tasklist chưa được đồng bộ thì đồng bộ tasklist trước
            if (!task.taskList.googleTaskListId) {
                await this.syncTaskListToGoogle(task.tasklist_id, userId, forceUpdate);
                await task.taskList.reload();
            }

            //lấy thông tin google task list
            const googleTasks = await getAuthenticatedClient(userId);
            let googleTask;

            //tạo request body
            const requestBody = {
                id: task.googleTaskId,
                title: task.title,
                notes: task.notes,
                due: formatDateForGoogleTasks(task.due),
                status: task.status,
                position: task.position,
                completed: formatDateForGoogleTasks(task.completed)
            };

            let parentTaskId = null;

            //nếu task có parent task thì đồng bộ parent task trước
            if (task.parent_task_id) {
                const parentTask = await Task.findByPk(task.parent_task_id);
                if (parentTask) {
                    // Đảm bảo parent task đã được đồng bộ lên Google Tasks
                    if (!parentTask.googleTaskId) {
                        //console.log('>> Parent task chưa được đồng bộ, đồng bộ parent task trước:', parentTask.id);
                        await this.syncTaskToGoogle(parentTask.id, userId, forceUpdate);
                        // Reload parent task để lấy googleTaskId mới được cập nhật
                        await parentTask.reload();
                    }
                    
                    // Lưu googleTaskId của parent để sử dụng trong API call
                    if (parentTask.googleTaskId) {
                        parentTaskId = parentTask.googleTaskId;
                    } 
                }
            }

            if (task.googleTaskId) {
                console.log('>> update task:', task.googleTaskId);
                try {
                    googleTask = await retryGoogleCall(
                        () => googleTasks.tasks.update({
                            tasklist: task.taskList.googleTaskListId,
                            task: task.googleTaskId,
                            requestBody: requestBody
                        }),
                        `update task "${task.title}"`,
                        { taskId: task.id, title: task.title, userId }
                    );
                    if (parentTaskId) {
                        await retryGoogleCall(
                            () => googleTasks.tasks.move({
                                tasklist: task.taskList.googleTaskListId,
                                task: task.googleTaskId,
                                parent: parentTaskId
                            }),
                            `move task "${task.title}" under parent`,
                            { taskId: task.id, parentTaskId, userId }
                        );
                    }
                    return googleTask.data;
                } catch (err) {
                    if (err?.response?.status === 404) {
                        console.warn(`Không tìm thấy task trên Google: "${task.title}" (${task.googleTaskId}) → Xóa local task.`);
                        await Task.destroy({ where: { id: task.id } });
                        return null;
                    } else {
                        throw err;
                    }
                }

            } else {
                console.log('>> insert task:', task.title);
                const insertParams = {
                    tasklist: task.taskList.googleTaskListId,
                    requestBody
                };
                
                if (parentTaskId) {
                    insertParams.parent = parentTaskId;
                }
                
                googleTask = await retryGoogleCall(
                    () => googleTasks.tasks.insert(insertParams),
                    `insert task "${task.title}"`,
                    { taskId: task.id, requestBody, parentTaskId, userId }
                );
                await Task.update(
                    { googleTaskId: googleTask.data.id },
                    { where: { id: taskId } }
                );
            }

            return googleTask.data;
        } catch (error) {
            console.error('Lỗi khi đồng bộ task:', error);
            throw error;
        }
    }

    static async syncAllTasksInList(taskListId, userId, forceUpdate = false) {
        try {
            const tasks = await Task.findAll({
                where: { tasklist_id: taskListId },
                order: [[sequelize.literal('CASE WHEN parent_task_id IS NULL THEN 0 ELSE 1 END ASC')]]
            });

            const results = await Promise.all(
                tasks.map(task => this.syncTaskToGoogle(task.id, userId, forceUpdate))
            );

            return results;
        } catch (error) {
            console.error('Lỗi khi đồng bộ tất cả tasks:', error);
            throw error;
        }
    }

    static async syncEntireBoard(boardId, userId, forceUpdate = false) {
        try {
            const board = await Board.findByPk(boardId);
            if (!board) throw new Error('Board không tồn tại');

            const taskLists = await TaskList.findAll({ where: { board_id: boardId } });

            const results = await Promise.all(
                taskLists.map(async (taskList) => {
                    const syncedTaskList = await this.syncTaskListToGoogle(taskList.id, userId, forceUpdate);
                    const syncedTasks = await this.syncAllTasksInList(taskList.id, userId, forceUpdate);
                    return { taskList: syncedTaskList, tasks: syncedTasks };
                })
            );

            await Board.update(
                { isGoogleSynced: true },
                { where: { id: boardId } }
            );

            return results;
        } catch (error) {
            console.error('Lỗi khi đồng bộ toàn bộ board:', error);
            throw error;
        }
    }

    // Tự động đồng bộ dữ liệu với Google Task
    static async autoSyncData() {
        try {
            console.log('Bắt đầu tự động đồng bộ dữ liệu...');
            
            // Lấy tất cả các board chưa được đồng bộ cùng với thông tin user
            const unSyncedBoards = await Board.findAll({
                where: { isGoogleSynced: false },
                include: [
                    {
                        model: sequelize.models.User,
                        as: 'createdByUser',
                        attributes: ['id', 'googleAccessToken', 'googleRefreshToken'],
                        where: {
                            googleAccessToken: {
                                [Op.ne]: null
                            },
                            googleRefreshToken: {
                                [Op.ne]: null
                            }
                        }
                    }
                ]
            });

            if (unSyncedBoards.length === 0) {
                console.log('Không có board nào cần đồng bộ');
                return { message: 'Không có board nào cần đồng bộ', syncedCount: 0 };
            }

            console.log(`Tìm thấy ${unSyncedBoards.length} board cần đồng bộ`);

            const syncResults = [];
            let successCount = 0;
            let errorCount = 0;

            // Đồng bộ từng board
            for (const board of unSyncedBoards) {
                try {
                    console.log(`Đang đồng bộ board "${board.name}" (ID: ${board.id}) của user ${board.createdByUser.id}`);
                    
                    const result = await this.syncEntireBoard(board.id, board.createdByUser.id, false);
                    
                    syncResults.push({
                        boardId: board.id,
                        boardName: board.name,
                        userId: board.createdByUser.id,
                        status: 'success',
                        data: result
                    });
                    
                    successCount++;
                    console.log(`Đồng bộ thành công board "${board.name}"`);
                    
                } catch (error) {
                    console.error(`Lỗi khi đồng bộ board "${board.name}":`, error.message);
                    
                    syncResults.push({
                        boardId: board.id,
                        boardName: board.name,
                        userId: board.createdByUser.id,
                        status: 'error',
                        error: error.message
                    });
                    
                    errorCount++;
                }
            }

            const summary = {
                message: `Hoàn thành tự động đồng bộ`,
                totalBoards: unSyncedBoards.length,
                successCount,
                errorCount,
                results: syncResults
            };

            console.log(`Tự động đồng bộ hoàn thành: ${successCount} thành công, ${errorCount} lỗi`);
            return summary;

        } catch (error) {
            console.error('Lỗi trong quá trình tự động đồng bộ:', error);
            throw error;
        }
    }


}

module.exports = AsyncService;
