{"name": "frontend", "homepage": "https://task.vuongnm.com", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.8.2", "antd": "^5.26.3", "axios": "^1.9.0", "axios-retry": "^4.5.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "ngrok": "^5.0.0-beta.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "redux-persist": "^6.0.0", "reselect": "^5.1.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}