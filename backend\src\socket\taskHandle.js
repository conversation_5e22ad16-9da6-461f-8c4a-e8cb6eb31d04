// <PERSON>à<PERSON> đăng ký các event socket cho task
function registerTaskSocketHandlers(socket, emitToBoard) {
    // Lắng nghe sự kiện sau khi task được tạo từ API
    socket.on("task:created", (data) => {
        const { boardId, task } = data;
        // console.log("Task created event received:", data);
        emitToBoard(boardId, "task:created", { boardId, task });
    });

    // Lắng nghe sự kiện sau khi task được cập nhật từ API
    socket.on("task:updated", (data) => {
        const { boardId, task } = data;
        // console.log("Task updated event received:", data);
        emitToBoard(boardId, "task:updated", { boardId, task });
    });

    // Lắng nghe sự kiện sau khi task bị xóa từ API
    socket.on("task:deleted", (data) => {
        const { boardId, taskId } = data;
        // console.log("Task deleted event received:", data);
        emitToBoard(boardId, "task:deleted", { boardId, taskId });
    });

    // Lắng nghe sự kiện sau khi task được đ<PERSON>h dấu hoàn thành từ API
    socket.on("task:completed", (data) => {
        const { boardId, task } = data;
        // console.log("Task completed event received:", data);
        emitToBoard(boardId, "task:completed", { boardId, task });
    });

    // Lắng nghe sự kiện sau khi task được di chuyển từ API
    socket.on("task:moved", (data) => {
        const { boardId, task } = data;
        // console.log("Task moved event received:", data);
        emitToBoard(boardId, "task:moved", { boardId, task });
    });
}

module.exports = { registerTaskSocketHandlers };
