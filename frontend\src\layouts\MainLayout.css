/* Main Layout Styles - <PERSON><PERSON> thừa từ Dashboard */
.dashboard-layout {
    height: 100vh;
    background: #f8f9fa;
}

/* Main content area */
.main-layout {
    background: #f8f9fa;
}

.dashboard-content {
    padding: 24px;
    height: calc(100vh - 64px);
    background: #f8f9fa;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Mobile sidebar overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Desktop - Optimized layout */
@media (min-width: 769px) {
    .dashboard-content {
        height: calc(100vh - 64px);
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-content {
        padding: 16px;
        min-height: calc(100vh - 56px);
        height: auto;
        overflow-y: auto;
    }
}

@media (max-width: 576px) {
    .dashboard-content {
        padding: 12px;
        height: auto;
    }
}

/* Layout utilities */
.layout-full-height {
    min-height: calc(100vh - 64px);
}

.layout-centered {
    display: flex;
    align-items: center;
    justify-content: center;
}

.layout-no-padding {
    padding: 0;
}

.layout-small-padding {
    padding: 12px;
}

.layout-large-padding {
    padding: 48px;
}
