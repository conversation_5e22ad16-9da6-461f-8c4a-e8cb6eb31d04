const boardService = require('../services/boardService');

const createBoard = async (req, res) => {
    try {
        const { workspaceId, name } = req.body;

        // workspaceId là bắt buộc
        if (!workspaceId) {
            return res.status(400).json({ message: 'workspaceId là bắt buộc' });
        }

        const board = await boardService.createBoard(workspaceId, name, req.user.id);
        res.status(201).json(board);
    } catch (error) {
        if (error.message === 'Workspace không tồn tại') {
            return res.status(404).json({ message: error.message });
        }
        if (error.message === 'Bạn không có quyền tạo board trong workspace này') {
            return res.status(403).json({ message: error.message });
        }
        res.status(500).json({ message: 'Lỗi khi tạo board', error: error.message });
    }
}

const getBoardsByUserId = async (req, res) => {
    try {
        const boards = await boardService.getBoardsByUserId(req.params.userId);
        res.status(200).json(boards);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy board', error: error.message });
    }
}

const getBoards = async (req, res) => {
    try {
        const { workspaceId } = req.query;
        const boards = await boardService.getBoards(req.user.id, workspaceId);
        res.status(200).json(boards);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy board', error: error.message });
    }
}

const updateBoard = async (req, res) => {
    try {
        const { id } = req.params;
        const { name } = req.body;
        const board = await boardService.updateBoard(id, name);
        res.status(200).json(board);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật board', error: error.message });
    }
}

const deleteBoard = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await boardService.deleteBoard(id);
        res.status(200).json(result);
    } catch (error) {
        if (error.message === 'Board không tồn tại') {
            return res.status(404).json({ message: error.message });
        }
        console.error('Lỗi khi xóa board:', error);
        res.status(500).json({ message: 'Lỗi khi xóa board', error: error.message });
    }
}

const getBoardByWorkspaceId = async (req, res) => {
    try {
        const { workspaceId } = req.params;
        const boards = await boardService.getBoardByWorkspaceId(workspaceId);
        res.status(200).json(boards);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy board', error: error.message });   
    }
}

module.exports = {
    createBoard,
    getBoards,
    updateBoard,
    deleteBoard,
    getBoardsByUserId,
    getBoardByWorkspaceId
}