const { DataTypes } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
    const Workspace = sequelize.define("Workspace", {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        description: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        ownerId: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        isDefault: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        }
    }, {
        tableName: 'workspaces',
        timestamps: true,
    });

    Workspace.associate = (models) => {
        Workspace.belongsTo(models.User, {
            foreignKey: 'ownerId',
            as: 'owner'
        });
        Workspace.hasMany(models.Board, {
            foreignKey: 'workspaceId',
            as: 'boards',
            onDelete: 'CASCADE'
        });
        Workspace.hasMany(models.WorkspaceAssignee, {
            foreignKey: 'workspace_id',
            as: 'workspaceAssignees',
            onDelete: 'CASCADE'
        });
    };

    return Workspace;
};