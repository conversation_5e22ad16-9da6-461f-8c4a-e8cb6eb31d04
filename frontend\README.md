# Dashboard Components

<PERSON><PERSON><PERSON> mục này chứa các component đ<PERSON><PERSON><PERSON> tách từ file `DashBoard.jsx` ban đầu để dễ quản lý và bảo trì hơn.

## C<PERSON>u trúc Components

### 1. TaskCard.jsx

-   **M<PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> thị từng task riêng lẻ
-   **<PERSON><PERSON>h năng**:
    -   Edit inline title, description, due date
    -   Delete task
    -   Hiển thị priority với màu sắc
    -   Hiển thị assignee và due date

### 2. AddTaskCard.jsx

-   **Mục đích**: Component để thêm task mới vào danh sách
-   **Tính năng**: <PERSON><PERSON> để thêm task mới

### 3. TaskList.jsx

-   **M<PERSON><PERSON> đích**: <PERSON><PERSON>n thị một danh sách task (column)
-   **T<PERSON>h năng**:
    -   Edit inline tên danh sách
    -   Delete danh sách
    -   <PERSON><PERSON><PERSON> thị số lượng task
    -   <PERSON><PERSON><PERSON> các TaskCard và AddTaskCard

### 4. AddTaskListCard.jsx

-   **<PERSON><PERSON><PERSON> đ<PERSON>**: Component đ<PERSON> thêm danh sách task mới
-   **Tính năng**: Click để thêm danh sách mới

### 5. DashboardHeader.jsx

-   **Mục đích**: Header của dashboard
-   **Tính năng**:
    -   Toggle sidebar
    -   Search input
    -   Notifications
    -   User avatar

### 6. DashboardSidebar.jsx

-   **Mục đích**: Sidebar chứa menu và thông tin
-   **Tính năng**:
    -   Danh sách boards
    -   Google Workspace integration
    -   Progress tracking

### 7. IntegrationBanner.jsx

-   **Mục đích**: Banner quảng cáo tích hợp Google Workspace
-   **Tính năng**: Hiển thị thông tin và button kết nối

## Custom Hook

### useTaskManager.js

-   **Mục đích**: Quản lý state và logic cho tasks
-   **Tính năng**:
    -   Quản lý danh sách tasks
    -   CRUD operations cho tasks và task lists
    -   State management tập trung

## Lợi ích của việc tách component

1. **Dễ bảo trì**: Mỗi component có trách nhiệm rõ ràng
2. **Tái sử dụng**: Các component có thể dùng ở nhiều nơi
3. **Testing**: Dễ dàng test từng component riêng lẻ
4. **Performance**: Có thể optimize từng component độc lập
5. **Collaboration**: Nhiều developer có thể làm việc song song

## Cách sử dụng

```jsx
import TaskCard from './components/TaskCard';
import TaskList from './components/TaskList';
import useTaskManager from './hooks/useTaskManager';

// Trong component cha
const { taskLists, addNewTask, deleteTask } = useTaskManager();

// Sử dụng component
<TaskList
    taskList={taskList}
    onAddTask={addNewTask}
    onDeleteTask={deleteTask}
/>;
```
