const exportService = require('../services/exportService');
const { Board } = require('../models');
const { getAuthenticatedSheetClient } = require('../middleware/googleAuth');

/**
 * Xuất dữ liệu từ board ra Google Sheet
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - Thông tin về spreadsheet đã tạo
 */
const exportBoardToSheet = async (req, res) => {
    try {
        const { boardId } = req.params;
        const userId = req.user.id;

        // Kiểm tra quyền truy cập board
        const board = await Board.findOne({
            where: {
                id: boardId,
                createdBy: userId
            }
        });

        if (!board) {
            return res.status(404).json({
                success: false,
                message: 'Board không tồn tại hoặc bạn không có quyền truy cập'
            });
        }

        // Gọi service để xuất dữ liệu
        const result = await exportService.exportBoardToSheet(boardId, userId);

        return res.status(200).json({
            success: true,
            message: '<PERSON><PERSON>t dữ liệu thành công',
            data: result
        });
    } catch (error) {
        console.error('Lỗi khi xuất dữ liệu:', error);
        return res.status(500).json({
            success: false,
            message: 'Đã xảy ra lỗi khi xuất dữ liệu',
            error: error.message
        });
    }
};

/**
 * Lấy danh sách các spreadsheet đã xuất
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Array} - Danh sách các spreadsheet đã xuất
 */
const getExportedSheets = async (req, res) => {
    try {
        const { boardId } = req.params;
        const userId = req.user.id;

        // Kiểm tra quyền truy cập board
        const board = await Board.findOne({
            where: {
                id: boardId,
                createdBy: userId
            }
        });

        if (!board) {
            return res.status(404).json({
                success: false,
                message: 'Board không tồn tại hoặc bạn không có quyền truy cập'
            });
        }

        // Lấy Google Sheets API client
        const sheets = await getAuthenticatedSheetClient(userId);

        // Tìm kiếm các spreadsheet có tên chứa tên của board
        const response = await sheets.files.list({
            q: `name contains '${board.name}' and mimeType='application/vnd.google-apps.spreadsheet'`,
            fields: 'files(id, name, webViewLink, createdTime)'
        });

        return res.status(200).json({
            success: true,
            message: 'Lấy danh sách spreadsheet thành công',
            data: response.data.files
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách spreadsheet:', error);
        return res.status(500).json({
            success: false,
            message: 'Đã xảy ra lỗi khi lấy danh sách spreadsheet',
            error: error.message
        });
    }
};

module.exports = {
    exportBoardToSheet,
    getExportedSheets
};
