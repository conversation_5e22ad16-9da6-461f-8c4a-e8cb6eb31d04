module.exports = (sequelize, DataTypes) => {
    const WorkspaceAssignee = sequelize.define('WorkspaceAssignee', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        workspaceId: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'workspace_assignees',
        timestamps: true
    });

    WorkspaceAssignee.associate = (models) => {
        WorkspaceAssignee.belongsTo(models.Workspace, {
            foreignKey: 'workspace_id',
            as: 'workspace'
        });
        WorkspaceAssignee.belongsTo(models.User, {
            foreignKey: 'userId',
            as: 'user'
        });

    }

    return WorkspaceAssignee;
}