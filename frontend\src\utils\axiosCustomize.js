import axios from 'axios';
import axiosRetry from 'axios-retry';
import { getToken, removeToken, setToken } from './tokenManager';

const baseURL = import.meta.env.VITE_API_URL;

const instance = axios.create({
    baseURL: baseURL,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
    }
});

// Cấu hình axios-retry
// axiosRetry(instance, {
//     retries: 3, // Số lần thử lại tối đa
//     retryDelay: (retryCount,error) => {
//         return retryCount * 1000;
//     }, // Thời gian chờ tăng dần giữa các lần thử
//     retryCondition: (error) => {
//         // Chỉ thử lại cho các lỗi mạng và timeout, không thử lại cho lỗi 401, 404, etc.
//         return axiosRetry.isNetworkOrIdempotentRequestError(error) && 
//                error.response?.status !== 401; // Không retry cho lỗi authentication
//     }
// });

// Request interceptor để thêm token vào header
instance.interceptors.request.use(
    (config) => {
        const token = getToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor để xử lý lỗi authentication và token refresh
instance.interceptors.response.use(
    (response) => {
        // Kiểm tra nếu server trả về token mới trong header
        const newToken = response.headers['x-new-access-token'];
        if (newToken) {
            // console.log('Cập nhật token mới từ server');
            setToken(newToken);
        }
        return response;
    },
    (error) => {
        if (error.response?.status === 401) {
            const errorData = error.response.data;
            
            // Kiểm tra nếu có token mới trong response header khi token expired
            const newToken = error.response.headers['x-new-access-token'];
            if (newToken) {
                // console.log('Token đã được refresh, thử lại request');
                setToken(newToken);
                
                // Retry request với token mới
                const originalRequest = error.config;
                originalRequest.headers.Authorization = `Bearer ${newToken}`;
                return instance(originalRequest);
            }
            
            // Nếu không có token mới hoặc refresh thất bại, logout
            if (errorData?.code === 'TOKEN_EXPIRED' || !newToken) {
                // console.log('Token hết hạn không thể refresh, chuyển về login');
                removeToken();
                window.location.href = '/';
            } else {
                // Các lỗi 401 khác
                removeToken();
                window.location.href = '/';
            }
        }
        return Promise.reject(error);
    }
);

export default instance;
