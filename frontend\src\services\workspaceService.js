import axios from '../utils/axiosCustomize';

const getWorkspaces = async () => {
    const response = await axios.get('/workspace');
    return response.data;
};

const getWorkspaceById = async (id) => {
    const response = await axios.get(`/workspace/${id}`);
    return response.data;
};

const createWorkspace = async (workspaceData) => {
    const response = await axios.post('/workspace', workspaceData);
    return response.data;
};

const updateWorkspace = async (id, workspaceData) => {
    const response = await axios.put(`/workspace/${id}`, workspaceData);
    return response.data;
};

const deleteWorkspace = async (id) => {
    const response = await axios.delete(`/workspace/${id}`);
    return response.data;
};

export default {
    getWorkspaces,
    getWorkspaceById,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace
}; 