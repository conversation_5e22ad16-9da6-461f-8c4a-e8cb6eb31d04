const { BoardAssignee, User, Board, TaskList, Task, TaskAssignee } = require('../models');
const notificationService = require('./notificationService');

const addAssigneeBoard = async (boardId, email) => {
    try {
        // Kiểm tra board tồn tại
        const board = await Board.findByPk(boardId, {
            include: [{
                model: User,
                as: 'createdByUser',
                attributes: ['id', 'fullName']
            }]
        });
        if (!board) {
            throw new Error('Board không tồn tại');
        }

        //Kiểm tra user tồn tại
        const user = await User.findOne({ where: { email } });
        if (!user) {
            throw new Error('User không tồn tại');
        }

        // Kiểm tra assignee đã tồn tại chưa
        const existingAssignee = await BoardAssignee.findOne({ 
            where: { 
                board_id: boardId, 
                user_id: user.id 
            } 
        });
        if (existingAssignee) {
            throw new Error('User đã đượ<PERSON> assign vào board này');
        }

        // Tạo assignee mới
        const newAssignee = await BoardAssignee.create({
            board_id: boardId,
            user_id: user.id,
        });
        
        // Tạo thông báo cho người được thêm vào board
        await notificationService.createNotification({
            userId: user.id,
            type: 'BOARD_INVITATION',
            content: `Bạn được ${board.createdByUser.fullName} thêm vào bảng làm việc "${board.name}"`,
            relatedId: boardId,
            relatedType: 'Board'
        });
        

        return newAssignee;
    } catch (error) {
        console.error('Error in addAssigneeBoard:', error);
        throw new Error('Lỗi khi thêm assignee vào board');
    }
}

const removeAssigneeFromBoard = async (boardId, email) => {
    try {
        // Kiểm tra board tồn tại
        const board = await Board.findByPk(boardId);
        if (!board) {
            throw new Error('Board không tồn tại');
        }

        // Kiểm tra user tồn tại
        const user = await User.findOne({ where: { email } });
        if (!user) {
            throw new Error('User không tồn tại');
        }

        // Kiểm tra assignee đã tồn tại chưa
        const existingAssignee = await BoardAssignee.findOne({ 
            where: { 
                board_id: boardId, 
                user_id: user.id 
            } 
        });
        if (!existingAssignee) {
            throw new Error('User không được assign vào board này');
        }

         // Lấy tất cả TaskList thuộc board này
         const taskLists = await TaskList.findAll({
            where: { board_id: boardId }
        });

        // Lấy tất cả Task trong các TaskList này
        const taskListIds = taskLists.map(taskList => taskList.id);
        const tasks = await Task.findAll({
            where: { tasklist_id: taskListIds }
        });

        // Xóa tất cả TaskAssignee của user này trong các task thuộc board
        if (tasks.length > 0) {
            const taskIds = tasks.map(task => task.id);
            await TaskAssignee.destroy({
                where: {
                    taskId: taskIds,
                    userId: user.id
                }
            });
        }

        // Xóa board assignee
        await existingAssignee.destroy();

        return existingAssignee;
    } catch (error) {
        throw new Error('Lỗi khi xóa assignee khỏi board');
    }
}

const getBoardAssignees = async (boardId) => {
    try {
        const assignees = await BoardAssignee.findAll({ 
            where: { board_id: boardId },
            include: [
                {
                    model: User,
                    as: 'user',
                    attributes: ['id', 'email', 'full_name', 'photo_url']
                }
            ]
        });
        return assignees;
    } catch (error) {
        throw new Error('Lỗi khi lấy assignee của board');
    }
}

const getUserAssignedBoards = async (userId) => {
    try {
        const boardAssignments = await BoardAssignee.findAll({
            where: { user_id: userId },
            include: [
                {
                    model: Board,
                    as: 'board',
                    attributes: ['id', 'name']
                }
            ]
        });

        // Chuyển đổi dữ liệu để phù hợp với frontend
        const boards = boardAssignments.map(assignment => ({
            id: assignment.board.id,
            name: assignment.board.name
        }));

        return boards;
    } catch (error) {
        throw new Error('Lỗi khi lấy danh sách board được gán cho user');
    }
}

module.exports = {
    addAssigneeBoard,
    removeAssigneeFromBoard,
    getBoardAssignees,
    getUserAssignedBoards
}