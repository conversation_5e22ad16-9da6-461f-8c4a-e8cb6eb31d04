const e = require("express");
const { registerTaskSocketHandlers } = require("./taskHandle");
const { registerTaskListSocketHandlers } = require("./tasklistHandle");

let io;

// Khởi tạo Socket.IO
function init(socketIo) {
    io = socketIo;

    // Xử lý kết nối socket
    io.on("connection", (socket) => {
        const count = getIO().engine.clientsCount;
        console.log(
            `Người dùng kết nối: ${socket.id} - Tổng số người dùng: ${count}`
        );

        //  Xử lý join workspace room
        socket.on("workspace:join", (workspaceId) => {
            const roomId = `workspace_${workspaceId}`;
            socket.join(roomId);
            console.log(
                `>> Client ${socket.id} joined workspace room ${roomId}`
            );
        });

        // Xử lý join board room
        socket.on("board:join", (boardId) => {
            const roomId = `board_${boardId}`;
            socket.join(roomId);
            console.log(`>> Client ${socket.id} joined board room ${roomId}`);
        });

        // Đăng ký các event socket cho task và task list
        registerTaskSocketHandlers(socket, emitToBoard);
        registerTaskListSocketHandlers(socket, emitToBoard);

        // Xử lý leave board room
        socket.on("board:leave", (boardId) => {
            const roomId = `board_${boardId}`;
            socket.leave(roomId);
            console.log(`>> Client ${socket.id} left board room ${roomId}`);
        });

        //Xử lý leave workspace room
        socket.on("workspace:leave", (workspaceId) => {
            const roomId = `workspace_${workspaceId}`;
            socket.leave(roomId);
            console.log(`>> Client ${socket.id} left workspace room ${roomId}`);
        });

        socket.on("disconnect", () => {
            const count = getIO().engine.clientsCount;
            console.log(
                `Người dùng ngắt kết nối: ${socket.id} - Tổng số người dùng: ${count}`
            );
        });
    });

    return io;
}

// Lấy đối tượng io đã khởi tạo
function getIO() {
    if (!io) {
        throw new Error("Socket.IO chưa được khởi tạo!");
    }
    return io;
}

// Gửi thông báo đến tất cả người dùng
function emitToAll(event, data) {
    getIO().emit(event, data);
}

// Gửi thông báo đến một board room
function emitToBoard(boardId, event, data) {
    const roomId = `board_${boardId}`;
    getIO().to(roomId).emit(event, data);
}

// Gửi thông báo đến một người dùng cụ thể
function emitToUser(socketId, event, data) {
    getIO().to(socketId).emit(event, data);
}

// Lấy danh sách clients trong một board room
function getBoardClients(boardId) {
    const roomId = `board_${boardId}`;
    const room = getIO().sockets.adapter.rooms.get(roomId);
    return room ? Array.from(room) : [];
}

module.exports = {
    init,
    getIO,
};
