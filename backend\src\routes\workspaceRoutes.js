const express = require('express');
const router = express.Router();
const workspaceController = require('../controllers/workspaceController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

// GET /api/workspaces - <PERSON><PERSON><PERSON> s<PERSON>ch workspace của user
router.get('/', isAuthenticated, handleTokenExpiration, workspaceController.getWorkspaces);

// GET /api/workspaces/:id - <PERSON><PERSON><PERSON> thông tin chi tiết workspace
router.get('/:id', isAuthenticated, handleTokenExpiration, workspaceController.getWorkspaceById);

// POST /api/workspaces - Tạo workspace mới
router.post('/', isAuthenticated, handleTokenExpiration, workspaceController.createWorkspace);

// PUT /api/workspaces/:id - Cập nhật workspace
router.put('/:id', isAuthenticated, handleTokenExpiration, workspaceController.updateWorkspace);

// DELETE /api/workspaces/:id - Xóa workspace
router.delete('/:id', isAuthenticated, handleTokenExpiration, workspaceController.deleteWorkspace);

module.exports = router; 