module.exports = (sequelize, DataTypes) => {
    const TaskAttachment = sequelize.define('TaskAttachment', {
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
      },
      taskId: {
        type: DataTypes.STRING(255),
        field: 'task_id',
        allowNull: false
      },
      fileName: {
        type: DataTypes.STRING(255),
        field: 'file_name',
        allowNull: false
      },
      webViewLink: {
        type: DataTypes.TEXT,
        field: 'web_view_link'
      },
      webContentLink: {
        type: DataTypes.TEXT,
        field: 'web_content_link'
      },
      fileSize: {
        type: DataTypes.BIGINT,
        field: 'file_size'
      },
      mimeType: {
        type: DataTypes.STRING(255),
        field: 'mime_type'
      },
      uploadedBy: {
        type: DataTypes.INTEGER,
        field: 'uploaded_by',
        allowNull: false
      }
    }, {
      tableName: 'task_attachments',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: false
    });
  
    TaskAttachment.associate = (models) => {
      TaskAttachment.belongsTo(models.Task, {
        foreignKey: 'task_id',
        as: 'task',
        onDelete: 'CASCADE'
      });
      TaskAttachment.belongsTo(models.User, {
        foreignKey: 'uploaded_by',
        as: 'uploader'
      });
    };
  
    return TaskAttachment;
  };