const { TaskAttachment, User, Task } = require('../models');
const AttachmentService = require('../services/attachmentService');


class AttachmentController {
    static async uploadAttachment(req, res) {
        try {
            const { taskId } = req.params;
            const files = req.files;
            const userId = req.user.id;

            if (!files || files.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Không có file nào được tải lên'
                });
            }

            //console.log(`Đang tải lên ${files.length} file cho task ${taskId}`);
            
            const uploadResults = [];
            const failedUploads = [];
            
            // Upload từng file
            for (const file of files) {
                try {
                    const attachment = await AttachmentService.uploadFileToTask(taskId, file, userId);
                    uploadResults.push(attachment);
                } catch (error) {
                    //console.error(`Lỗi khi tải file ${file.originalname}:`, error);
                    failedUploads.push({
                        fileName: file.originalname,
                        error: error.message
                    });
                }
            }

            // Trả về kết quả
            res.status(201).json({
                success: true,
                message: `Đã tải lên ${uploadResults.length}/${files.length} file thành công`,
                data: uploadResults,
                failed: failedUploads
            });
        } catch (error) {
            console.error('Error uploading attachments:', error);
            res.status(500).json({ 
                success: false,
                message: 'Lỗi khi upload file',
                error: error.message
            });
        }
    }

    static async deleteAttachment(req, res) {
        try {
            const { taskId, fileId } = req.params;
            
            // Kiểm tra xem req.user có tồn tại không
            if (!req.user || !req.user.id) {
                return res.status(401).json({ 
                    success: false,
                    message: 'Unauthorized - User not authenticated' 
                });
            }
            
            const userId = req.user.id;

            const result = await AttachmentService.deleteFileFromTask(taskId, fileId, userId);

            res.status(200).json({
                success: true,
                message: 'Xóa file thành công',
                data: result
            })
        } catch (error) {
            console.error('Error deleting attachment:', error);
            res.status(500).json({ 
                success: false,
                message: 'Lỗi khi xóa file',
                error: error.message
            });
        }
    }

    static async getTaskAttachments(req, res) {
        try {
            const { taskId } = req.params;
            const attachments = await AttachmentService.getTaskAttachments(taskId);

            res.status(200).json({
                success: true,
                message: 'Lấy danh sách file thành công',
                data: attachments
            })
        }catch(error){
            console.log("Error getting attachments:", error);
            res.status(500).json({
                success: false,
                message: 'Lỗi khi lấy danh sách file',
                error: error.message
            })
        }
    }
}

module.exports = AttachmentController;
