
// Lưu token vào localStorage
export const setToken = (token) => {
    if (!token) {
        console.error('Không thể lưu token rỗng');
        return;
    }
    // console.log('Lưu access_token vào localStorage:', token.substring(0, 10) + '...');
    localStorage.setItem('access_token', token);
    
    // Kiểm tra xem access_token đã được lưu chưa
    const savedToken = localStorage.getItem('access_token');
    if (savedToken !== token) {
        console.error('Access_token không được lưu chính xác vào localStorage');
    } else {
        // console.log('Token đã được lưu thành công vào localStorage');
    }
};

// Lấy token từ localStorage
export const getToken = () => {
    return localStorage.getItem('access_token');
};

// Xóa token
export const removeToken = () => {
    localStorage.removeItem('access_token');
};

export default {
    setToken,
    getToken,
    removeToken
};