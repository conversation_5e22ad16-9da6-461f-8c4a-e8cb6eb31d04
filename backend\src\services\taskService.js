const { v4: uuidv4 } = require("uuid");
const { Task, Board, TaskList } = require("../models");
const { getNextPosition, getMiddlePosition } = require("../utils/position");
const { Op, Sequelize } = require("sequelize");

class TaskService {
    async getAllTasks(taskListId) {
        return await Task.findAll({
            where: { tasklist_id: taskListId },
            order: [["position", "ASC"]],
        });
    }

    async getTask(taskId) {
        return await Task.findByPk(taskId);
    }

    async createTask(taskListId, newTask, userId) {
        const tasks = await Task.findAll({
            where: { tasklist_id: taskListId },
            order: [["position", "ASC"]],
        });

        const newTaskPosition = this.getNewTaskPosition(tasks);

        return await Task.create({
            id: uuidv4(),
            title: newTask.title,
            tasklist_id: taskListId,
            notes: newTask.notes,
            status: "needsAction",
            position: newTaskPosition,
            priority: newTask.priority,
            assignee: newTask.assignee,
            created_by: userId,
        });
    }

    async createSubtask(taskListId, parentTaskId, newTask, userId) {
        const parentTask = await Task.findByPk(parentTaskId);
        if (!parentTask) {
            throw new Error("Task cha không tồn tại");
        }

        if (parentTask.parent_task_id) {
            throw new Error(
                "Không thể tạo task con của task con. Chỉ hỗ trợ 1 cấp."
            );
        }

        const tasks = await Task.findAll({
            where: { tasklist_id: taskListId },
            order: [["position", "ASC"]],
        });

        const newTaskPosition = this.getNewTaskPosition(tasks);

        return await Task.create({
            id: uuidv4(),
            title: newTask.title,
            tasklist_id: taskListId,
            notes: newTask.notes,
            status: "needsAction",
            position: newTaskPosition,
            priority: newTask.priority,
            assignee: newTask.assignee,
            created_by: userId,
            parent_task_id: parentTaskId,
        });
    }

    async updateTask(taskId, taskFields) {
        const dbUpdateData = {};
        if (taskFields.title !== undefined)
            dbUpdateData.title = taskFields.title;
        if (taskFields.notes !== undefined)
            dbUpdateData.notes = taskFields.notes;
        if (taskFields.priority !== undefined)
            dbUpdateData.priority = taskFields.priority;
        if (taskFields.assignee !== undefined)
            dbUpdateData.assignee = taskFields.assignee;
        if (taskFields.due !== undefined) dbUpdateData.due = taskFields.due;
        if (taskFields.status !== undefined) {
            dbUpdateData.status = taskFields.status;
            dbUpdateData.completed =
                taskFields.status === "completed" ? new Date() : null;
        }
        if (taskFields.completed !== undefined) {
            dbUpdateData.completed = taskFields.completed;
        }

        await Task.update(dbUpdateData, {
            where: { id: taskId },
        });

        return await Task.findByPk(taskId);
    }

    async completeTask(taskId) {
        await Task.update(
            {
                status: "completed",
                completed: new Date(),
            },
            {
                where: { id: taskId },
            }
        );

        return await Task.findByPk(taskId);
    }

    async unCompleteTask(taskId) {
        await Task.update(
            {
                status: "needsAction",
                completed: null,
            },
            {
                where: { id: taskId },
            }
        );

        return await Task.findByPk(taskId);
    }

    async deleteTask(taskId) {
        return await Task.destroy({
            where: { id: taskId },
        });
    }

    async moveTask(taskId, newTaskListId, parent, previous, next) {
        // Lấy task hiện tại
        const currentTask = await Task.findByPk(taskId);
        if (!currentTask) {
            throw new Error('Task không tồn tại');
        }

        // Tính toán position mới
        let newPosition;
        
        if (!previous && !next) {
            // Nếu không có task trước và sau, đặt ở đầu danh sách
            newPosition = getNextPosition("0");
        } else if (previous && !next) {
            // Nếu chỉ có task trước, đặt ở cuối
            const previousTask = await Task.findOne({
                where: { id: previous }
            });
            
            if (previousTask) {
                // Tính position mới dựa trên position của task trước đó
                newPosition = getNextPosition(previousTask.position);
            } else {
                // Nếu không tìm thấy task trước đó, đặt ở đầu
                newPosition = getNextPosition("0");
            }
        } else if (!previous && next) {
            // Nếu chỉ có task sau, đặt ở đầu
            const nextTask = await Task.findOne({
                where: { id: next }
            });
            
            if (nextTask) {
                // Tính position mới dựa trên position của task sau đó
                newPosition = getMiddlePosition("0", nextTask.position);
            } else {
                // Nếu không tìm thấy task sau đó, đặt ở đầu
                newPosition = getNextPosition("0");
            }
        } else {
            // Có cả task trước và sau - đặt ở giữa
            const previousTask = await Task.findOne({
                where: { id: previous }
            });
            const nextTask = await Task.findOne({
                where: { id: next }
            });
            
            if (previousTask && nextTask) {
                // Tính position mới ở giữa hai task
                newPosition = getMiddlePosition(previousTask.position, nextTask.position);
            } else if (previousTask) {
                // Chỉ có task trước
                newPosition = getNextPosition(previousTask.position);
            } else if (nextTask) {
                // Chỉ có task sau
                newPosition = getMiddlePosition("0", nextTask.position);
            } else {
                // Không có task nào, đặt ở đầu
                newPosition = getNextPosition("0");
            }
        }

        // Cập nhật task với thông tin mới
        await Task.update(
            {
                tasklist_id: newTaskListId,
                parent_task_id: parent || null,
                position: newPosition,
            },
            {
                where: { id: taskId },
            }
        );

        return await Task.findByPk(taskId);
    }

    async clearCompletedTasks(taskListId) {
        return await Task.destroy({
            where: {
                tasklist_id: taskListId,
                status: "completed",
            },
        });
    }

    async searchTasks(query, boardId) {
        const taskLists = await TaskList.findAll({
            where: { board_id: boardId },
        });
        const taskListIds = taskLists.map((list) => list.id);

        const tasks = await Task.findAll({
            where: {
                tasklist_id: { [Op.in]: taskListIds },
                [Op.or]: [
                    { title: { [Op.like]: `%${query}%` } },
                    { notes: { [Op.like]: `%${query}%` } },
                ],
            },
            include: [
                {
                    model: TaskList,
                    as: "taskList",
                    order: [["position", "ASC"]],
                },
            ],
            order: [["position", "ASC"]],
        });

        const grouped = {};
        tasks.forEach((task) => {
            const listId = task.taskList.id;
            if (!grouped[listId]) {
                grouped[listId] = {
                    ...task.taskList.dataValues,
                    tasks: [],
                };
            }
            grouped[listId].tasks.push(task);
        });

        return Object.values(grouped);
    }

    async markBoardForSync(boardId) {
        await Board.update(
            {
                isGoogleSynced: false,
            },
            {
                where: { id: boardId },
            }
        );
    }

    getNewTaskPosition(tasks) {
        try {
            if (!tasks || tasks.length === 0) return getNextPosition("0");

            const lastTask = tasks[tasks.length - 1];
            const lastPosition = lastTask?.position;

            if (!lastPosition) {
                console.warn(
                    "Task cuối không có position, trả về position mới"
                );
                return getNextPosition("0");
            }

            return getNextPosition(lastPosition);
        } catch (error) {
            console.error("Lỗi khi tạo position mới:", error);
            throw new Error("Lỗi khi tạo position mới");
        }
    }
}

module.exports = new TaskService();
