const { google } = require('googleapis');
const { getAuthenticatedDriveClient } = require('../middleware/googleAuth');
const fs = require('fs');
const { Readable } = require('stream');

const ensureUserFolder = async (drive, userId) => {
    const folderName = `user-${userId}`;

    // Kiểm tra xem thư mục đã tồn tại chưa
    const res = await drive.files.list({
        q: `name='${folderName}' and mimeType='application/vnd.google-apps.folder' and trashed=false`,
        fields: 'files(id, name)',
        spaces: 'drive',
    });

    if (res.data.files.length > 0) {
        return res.data.files[0].id;
    }

    // Nếu chưa có thì tạo mới
    const fileMetadata = {
        name: folderName,
        mimeType: 'application/vnd.google-apps.folder',
    };

    const folder = await drive.files.create({
        requestBody: fileMetadata,
        fields: 'id',
    });

    return folder.data.id;
};

/**
 * Upload file lên Google Drive vào thư mục riêng của người dùng
 */
const uploadFileToDrive = async (file, userId) => {
    try {
        const drive = await getAuthenticatedDriveClient(userId);

        // Tạo hoặc lấy thư mục người dùng
        const folderId = await ensureUserFolder(drive, userId);

        let mediaBody;
        if (file.buffer) {
            mediaBody = Readable.from(file.buffer);
        } else if (file.path && fs.existsSync(file.path)) {
            mediaBody = fs.createReadStream(file.path);
        } else {
            throw new Error('Không tìm thấy dữ liệu file');
        }

        const response = await drive.files.create({
            requestBody: {
                name: file.originalname,
                mimeType: file.mimetype,
                parents: [folderId]
            },
            media: {
                mimeType: file.mimetype || file.mimeType,
                body: mediaBody
            },
            fields: 'id, name, mimeType, webViewLink, webContentLink, size, createdTime'
        });

        if (file.path && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
        }

        const fileId = response.data.id;
        await setFilePublic(fileId, userId);

        return response.data;
    } catch (error) {
        console.error("Upload error:", error.message);
        throw error;
    }
};

const deleteFileFromDrive = async (fileId, userId) => {
    try {
        const drive = await getAuthenticatedDriveClient(userId);
        const response = await drive.files.delete({
            fileId: fileId
        });
        return response.data;
    } catch (error) {
        console.error("Delete error:", error.message);
        throw error;
    }
}

const setFilePublic = async (fileId, userId) => {
    const drive = await getAuthenticatedDriveClient(userId);
    const response = await drive.permissions.create({
        fileId: fileId,
        requestBody: {
            role: 'reader',
            type: 'anyone'
        }
    });

    return response.data;
}

module.exports = { uploadFileToDrive, deleteFileFromDrive, setFilePublic };