import axios from '../utils/axiosCustomize'

export const getNotifications = async () => {
    const response = await axios.get('/notifications');
    return response.data;
}

export const deleteNotification = async (notificationId) => {
    const response = await axios.delete(`/notifications/${notificationId}`);
    return response.data;
}

export const markAsRead = async (notificationId) => {
    const response = await axios.put(`/notifications/${notificationId}/read`);
    return response.data;
}

export const markAllAsRead = async () => {
    const response = await axios.put('/notifications/read');
    return response.data;
}

export const deleteAllNotifications = async () => {
    const response = await axios.delete('/notifications');
    return response.data;
}