const { Workspace, Board, User } = require('../models');

const createWorkspace = async (name, description, ownerId) => {
    try {
        if (!name) {
            throw new Error('Tên workspace là bắt buộc');
        }

        const workspace = await Workspace.create({
            name,
            description: description || '',
            ownerId
        });

        return workspace;
    } catch (error) {
        throw new Error(`Lỗi khi tạo workspace: ${error.message}`);
    }
};

const getWorkspacesByOwner = async (ownerId) => {
    try {
        const workspaces = await Workspace.findAll({
            where: { ownerId },
            order: [['created_at', 'ASC']]
        });

        return workspaces;
    } catch (error) {
        throw new Error(`Lỗi khi lấy danh sách workspace: ${error.message}`);
    }
};

const getWorkspaceById = async (id, ownerId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                id,
                ownerId
            },
            include: [
                {
                    model: Board,
                    as: 'boards',
                    attributes: ['id', 'name', 'isGoogleSynced']
                },
                {
                    model: User,
                    as: 'owner',
                    attributes: ['id', 'email', 'fullName']
                }
            ]
        });

        if (!workspace) {
            throw new Error('Workspace không tồn tại hoặc bạn không có quyền truy cập');
        }

        return workspace;
    } catch (error) {
        throw new Error(`Lỗi khi lấy thông tin workspace: ${error.message}`);
    }
};

const updateWorkspace = async (id, updateData, ownerId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                id,
                ownerId
            }
        });

        if (!workspace) {
            throw new Error('Workspace không tồn tại hoặc bạn không có quyền chỉnh sửa');
        }

        await workspace.update(updateData);
        return workspace;
    } catch (error) {
        throw new Error(`Lỗi khi cập nhật workspace: ${error.message}`);
    }
};

const deleteWorkspace = async (id, ownerId) => {
    try {
        const workspace = await Workspace.findOne({
            where: {
                id,
                ownerId
            }
        });

        if (!workspace) {
            throw new Error('Workspace không tồn tại hoặc bạn không có quyền xóa');
        }

        // Xóa tất cả các board trong workspace
        await Board.destroy({
            where: {
                workspaceId: id
            }
        });

        // Sau khi xóa tất cả board, xóa workspace
        await workspace.destroy();
        return { message: 'Workspace và tất cả board đã được xóa thành công' };
    } catch (error) {
        throw new Error(`Lỗi khi xóa workspace: ${error.message}`);
    }
};


module.exports = {
    createWorkspace,
    getWorkspacesByOwner,
    getWorkspaceById,
    updateWorkspace,
    deleteWorkspace
}; 