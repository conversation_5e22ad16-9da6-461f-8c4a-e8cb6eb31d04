import { useState, useEffect } from "react";
import { useMessage } from "../contexts/MessageContext";
import TaskListService from "../services/taskListService";
import exportService from "../services/exportService";
import { useSocket } from "../contexts/SocketContext";

const useTaskList = (selectedBoard, refreshBoards) => {
    const [taskLists, setTaskLists] = useState([]);
    const [taskListLoading, setTaskListLoading] = useState(false);
    const [isCreatingTaskList, setIsCreatingTaskList] = useState(false);
    const [isUpdatingTaskList, setIsUpdatingTaskList] = useState(false);
    const [isDeletingTaskList, setIsDeletingTaskList] = useState(false);
    const [isExporting, setIsExporting] = useState(false);
    const [error, setError] = useState(null);
    const { success, error: showError } = useMessage();
    const {
        socket,
        connected,
        emitTaskListCreated,
        emitTaskListUpdated,
        emitTaskListDeleted,
        emitTaskListMoved,
    } = useSocket();

    useEffect(() => {
        if (selectedBoard) {
            fetchTaskLists();
        }
    }, [selectedBoard]);

    useEffect(() => {
        if (!socket || !connected || !selectedBoard) return;

        //Lắng nghe các sự kiện từ server
        socket.on("taskList:created", (data) => {
            const newTaskList = {
                ...data,
                name: data.title,
                tasks: data.tasks || [],
                color: data.color || "#1890ff",
            };
            setTaskLists((prev) => [...prev, newTaskList]);
        });

        socket.on("taskList:updated", (data) => {
            setTaskLists((prev) =>
                prev.map((list) =>
                    list.id === data.id
                        ? {
                              ...list,
                              ...data,
                              name: data.title,
                              tasks: data.tasks || list.tasks,
                              color: data.color || list.color,
                          }
                        : list
                )
            );
        });

        socket.on("taskList:deleted", (data) => {
            setTaskLists((prev) => prev.filter((list) => list.id !== data.id));
        });

        socket.on("taskList:moved", (data) => {
            setTaskLists((prev) =>
                prev.map((list) =>
                    list.id === data.id
                        ? {
                              ...list,
                              order: data.order,
                          }
                        : list
                )
            );
        });

        return () => {
            socket.off("taskList:created");
            socket.off("taskList:updated");
            socket.off("taskList:deleted");
            socket.off("taskList:moved");
        };
    }, [socket, connected, selectedBoard]);

    const fetchTaskLists = async () => {
        try {
            // console.log('Fetching task lists for board:', selectedBoard);
            setTaskListLoading(true);
            setError(null);
            const response = await TaskListService.getTaskListByBoardId(
                selectedBoard
            );
            // console.log('TaskList response:', response);

            const mappedTaskLists = response.map((taskList) => ({
                ...taskList,
                name: taskList.title,
                tasks: taskList.tasks || [],
                color: taskList.color || "#1890ff",
            }));

            setTaskLists(mappedTaskLists);
        } catch (error) {
            console.error("Lỗi khi tải danh sách công việc:", error);
            setError("Không thể tải danh sách công việc");
            showError(
                "Không thể tải danh sách công việc: " +
                    (error.response?.data?.message || error.message)
            );
            setTaskLists([]);
        } finally {
            setTaskListLoading(false);
        }
    };

    const addNewTaskList = async () => {
        const taskListData = {
            title: "Danh sách công việc mới",
            description: "Thêm mô tả ...",
            color: "#1890ff",
        };

        try {
            setIsCreatingTaskList(true);
            const response = await TaskListService.createTaskList(
                selectedBoard,
                taskListData
            );
            // console.log('TaskList response:', response);

            const newTaskList = {
                ...response,
                name: response.title,
                tasks: response.tasks || [],
                color: response.color || "#1890ff",
            };

            // Emit socket event ngay lập tức sau khi tạo thành công
            emitTaskListCreated({
                boardId: selectedBoard,
                taskList: newTaskList,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            success("Tạo danh sách công việc thành công!");
            return newTaskList;
        } catch (error) {
            console.error("Lỗi khi tạo danh sách công việc:", error);
            showError(
                "Không thể tạo danh sách công việc: " +
                    (error.response?.data?.message || error.message)
            );
        } finally {
            setIsCreatingTaskList(false);
        }
    };

    const deleteTaskList = async (listId) => {
        try {
            setIsDeletingTaskList(true);

            // Kiểm tra xem có phải tasklist mặc định không
            const isDefaultTaskList = taskLists.find(
                (list) => list.id === listId && list.isDefault
            );
            if (isDefaultTaskList) {
                showError("Không thể xóa danh sách công việc mặc định");
                return;
            }

            // Gọi API để xóa tasklist
            await TaskListService.deleteTaskList(selectedBoard, listId);

            // Emit socket event ngay sau khi xóa thành công
            emitTaskListDeleted({
                boardId: selectedBoard,
                taskListId: listId,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            success("Xóa danh sách công việc thành công!");
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể xóa danh sách công việc: " + error.message);
                throw new Error("Không thể xóa danh sách công việc");
            }
        } finally {
            setIsDeletingTaskList(false);
        }
    };

    const updateTaskList = async (listId, updatedList) => {
        try {
            setIsUpdatingTaskList(true);
            
            if (
                updatedList.title === undefined &&
                updatedList.description === undefined
            ) {
                return;
            }

            //Nếu để trống title thì không cập nhật
            if (updatedList.title === "") {
                showError("Tiêu đề không được để trống");
                return;
            }

            setTaskLists(
                taskLists.map((list) =>
                    list.id === listId ? { ...list, ...updatedList } : list
                )
            );


            const payload = {
                title: updatedList.title,
                description: updatedList.description,
                boardId: selectedBoard,
            };

            const response = await TaskListService.updateTaskList(
                listId,
                payload
            );

            // Emit socket event
            emitTaskListUpdated({
                boardId: selectedBoard,
                taskList: response,
            });

            // Refresh boards để cập nhật trạng thái đồng bộ
            if (refreshBoards) {
                await refreshBoards();
            }

            success("Cập nhật danh sách công việc thành công!");
        } catch (error) {
            if(error.response?.status === 404 || error.response?.data?.code === 404) {
                showError("Xung đột dữ liệu với Google Task. Vui lòng ấn nút đồng bộ để cập nhật lại dữ liệu.");
                throw new Error("Xung đột dữ liệu với Google Task");
            }else {
                showError("Không thể cập nhật danh sách công việc: " + error.message);
                throw new Error("Không thể cập nhật danh sách công việc");
            }
        } finally {
            setIsUpdatingTaskList(false);
        }
    };

    const exportBoardToSheet = async () => {
        try {
            setIsExporting(true);
            const response = await exportService.exportBoardToSheet(
                selectedBoard
            );
            window.open(response.data.spreadsheetUrl, "_blank");
            success("Xuất bảng ra Google Sheet thành công!");
        } catch (error) {
            console.error("Lỗi khi xuất bảng ra Google Sheet:", error);
            showError(
                "Không thể xuất bảng ra Google Sheet: " +
                    (error.response?.data?.message || error.message)
            );
        } finally {
            setIsExporting(false);
        }
    };

    return {
        taskLists,
        taskListLoading,
        isCreatingTaskList,
        isUpdatingTaskList,
        isDeletingTaskList,
        isExporting,
        error,
        setTaskLists,
        fetchTaskLists,
        addNewTaskList,
        deleteTaskList,
        updateTaskList,
        exportBoardToSheet,
    };
};

export default useTaskList;
