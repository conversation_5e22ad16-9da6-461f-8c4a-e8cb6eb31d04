const { TaskAssignee, User, Task, TaskList, Board, Workspace } = require('../models');
const notificationService = require('./notificationService');

/**
 * Thêm assignee cho task
 */
const addAssigneeToTask = async (taskId, email) => {
  try {
    // Kiểm tra task tồn tại
    const task = await Task.findByPk(taskId, {
      include: [{
        model: User,
        as: 'creator',
        attributes: ['id', 'fullName']
      }]
    });
    if (!task) {
      throw new Error('Task không tồn tại');
    }

    // Kiểm tra user tồn tại
    const user = await User.findOne({ where: { email } });
    if (!user) {
      throw new Error('User không tồn tại');
    }

    // Kiểm tra assignee đã tồn tại chưa
    const existingAssignee = await TaskAssignee.findOne({
      where: { taskId, userId: user.id }
    });

    if (existingAssignee) {
      return existingAssignee;
    }

    // Tạo assignee mới
    const newAssignee = await TaskAssignee.create({
      taskId,
      userId: user.id,
      fullName: user.fullName,
      email: user.email
    });

    // Tạo thông báo cho người được assign
    await notificationService.createNotification({
      userId: user.id,
      type: 'TASK_ASSIGNED',
      content: `Bạn được ${task.creator.fullName} giao công việc "${task.title}"`,
      relatedId: taskId,
      relatedType: 'Task'
    });

    return newAssignee;
  } catch (error) {
    throw error;
  }
};

/**
 * Xóa assignee khỏi task
 */
const removeAssigneeFromTask = async (taskId, email) => {
  try {
    const user = await User.findOne({ where: { email } });
    if (!user) {
      throw new Error('User không tồn tại');
    }

    const deletedCount = await TaskAssignee.destroy({
      where: { taskId, userId: user.id }
    });

    return deletedCount > 0;
  } catch (error) {
    throw error;
  }
};

/**
 * Lấy danh sách assignees của task
 */
const getTaskAssignees = async (taskId) => {
  try {
    const assignees = await TaskAssignee.findAll({
      where: { taskId },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'full_name', 'email', 'photo_url']
      }],
      order: [['assignedAt', 'ASC']]
    });

    return assignees;
  } catch (error) {
    throw error;
  }
};

/**
 * Cập nhật toàn bộ assignees cho task
 */
const updateTaskAssignees = async (taskId, emails) => {
  try {
    // Lấy thông tin task
    const task = await Task.findByPk(taskId, {
      include: [{
        model: User,
        as: 'creator',
        attributes: ['id', 'full_name']
      }]
    });

    if (!task) {
      throw new Error('Task không tồn tại');
    }

    // Lấy danh sách assignee hiện tại
    const currentAssignees = await TaskAssignee.findAll({
      where: { taskId },
      attributes: ['userId', 'email']
    });
    const currentEmails = currentAssignees.map(a => a.email);

    // Xóa tất cả assignees hiện tại
    await TaskAssignee.destroy({ where: { taskId } });

    // Thêm assignees mới
    if (emails && emails.length > 0) {
      const users = await User.findAll({
        where: { email: emails }
      });

      const assigneesToCreate = users.map(user => ({
        taskId,
        userId: user.id,
        fullName: user.full_name,
        email: user.email
      }));

      await TaskAssignee.bulkCreate(assigneesToCreate);

      // Tạo thông báo cho các assignee mới
      for (const user of users) {
        // Chỉ tạo thông báo cho những người mới được assign
        if (!currentEmails.includes(user.email)) {
          await notificationService.createNotification({
            userId: user.id,
            type: 'TASK_ASSIGNED',
            content: `Bạn được ${task.creator.full_name} giao nhiệm vụ "${task.title}"`,
            relatedId: taskId,
            relatedType: 'Task'
          });
        }
      }
    }

    // Trả về danh sách assignees mới
    return await getTaskAssignees(taskId);
  } catch (error) {
    throw error;
  }
};

/**
 * Xóa tất cả assignees của task
 */
const clearTaskAssignees = async (taskId) => {
  try {
    const deletedCount = await TaskAssignee.destroy({
      where: { taskId }
    });

    return deletedCount;
  } catch (error) {
    throw error;
  }
};

/**
 * Lấy danh sách tasks được assign cho user
 */
const getUserAssignedTasks = async (userId) => {
  try {
    const assignees = await TaskAssignee.findAll({
      where: { userId: userId },
      include: [{
        model: Task,
        as: 'task',
        include: [{
          model: User,
          as: 'creator',
          attributes: ['id', 'full_name', 'email', 'photo_url']
        }, {
          model: TaskList,
          as: 'taskList',
          attributes: ['id', 'title', 'description'],
          required: false,
          include: [{
            model: Board,
            as: 'board',
            attributes: ['id', 'name'],
            required: false,
            include: [{
              model: Workspace,
              as: 'workspace',
              attributes: ['id', 'name'],
              required: false
            }]
          }]
        }]
      }],
      order: [['assignedAt', 'DESC']]
    });

    // Chỉ trả về task data, thêm thông tin assignedAt từ TaskAssignee
    const tasks = assignees.map(assignee => {
      if (!assignee.task) return null;
      
      return {
        ...assignee.task.toJSON(),
        assignedAt: assignee.assignedAt // Thêm thời gian được assign
      };
    }).filter(task => task !== null);

    return tasks;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  addAssigneeToTask,
  removeAssigneeFromTask,
  getTaskAssignees,
  updateTaskAssignees,
  clearTaskAssignees,
  getUserAssignedTasks
};