const express = require('express');
const router = express.Router();
const { assignUserToWorkspace, unassignUserFromWorkspace, getWorkspaceAssignees, getUserAssignedWorkspaces } = require('../controllers/workspaceAssignController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

router.post('/assign', isAuthenticated, handleTokenExpiration, assignUserToWorkspace);
router.post('/unassign', isAuthenticated, handleTokenExpiration, unassignUserFromWorkspace);
router.get('/:workspaceId', isAuthenticated, handleTokenExpiration, getWorkspaceAssignees);
router.get('/user/:userId', isAuthenticated, handleTokenExpiration, getUserAssignedWorkspaces);

module.exports = router;
