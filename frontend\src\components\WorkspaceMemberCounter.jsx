import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Badge, Tooltip, Spin } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import workspaceAssignService from '../services/workspaceAssignService';

const WorkspaceMemberCounter = ({ workspaceId, size = 'small' }) => {
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(false);

    const fetchMembers = async () => {
        if (!workspaceId) return;

        try {
            setLoading(true);
            const response = await workspaceAssignService.getWorkspaceAssignees(
                workspaceId
            );
            setMembers(response);
        } catch (error) {
            console.error('Error fetching workspace members:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchMembers();
    }, [workspaceId]);

    // Lắng nghe sự kiện workspace member removal để refresh member count
    useEffect(() => {
        const handleWorkspaceMemberRemoved = (event) => {
            const { workspaceId: removedWorkspaceId } = event.detail;
            // Kiểm tra xem có phải workspace hiện tại không
            if (workspaceId === removedWorkspaceId) {
                fetchMembers();
            }
        };

        window.addEventListener(
            'workspace:member_removed',
            handleWorkspaceMemberRemoved
        );

        return () => {
            window.removeEventListener(
                'workspace:member_removed',
                handleWorkspaceMemberRemoved
            );
        };
    }, [workspaceId]);

    if (loading) {
        return <Spin size='small' />;
    }

    const memberCount = members.length;
    const displayMembers = members.slice(0, 3); // Hiển thị tối đa 3 avatar đầu tiên

    return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Avatar.Group maxCount={3} size={size}>
                {displayMembers.map((member, index) => {
                    const user = member.User || member;
                    return (
                        <Tooltip
                            key={user.id || index}
                            title={user.fullName || user.email}
                        >
                            <Avatar
                                src={user.photoUrl}
                                icon={<UserOutlined />}
                                size={size}
                            />
                        </Tooltip>
                    );
                })}
            </Avatar.Group>

            {memberCount > 3 && (
                <span
                    style={{
                        fontSize: '12px',
                        color: '#666',
                        marginLeft: '4px'
                    }}
                >
                    +{memberCount - 3}
                </span>
            )}
        </div>
    );
};

export default WorkspaceMemberCounter;
