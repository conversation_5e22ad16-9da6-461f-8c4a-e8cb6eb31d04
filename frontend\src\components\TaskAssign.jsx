import React, { useState, useEffect } from 'react';
import {
    Card,
    Tag,
    Typography,
    Space,
    Checkbox,
    Avatar,
    Spin,
    Empty
} from 'antd';
import {
    CheckCircleTwoTone,
    ClockCircleOutlined,
    UserOutlined
} from '@ant-design/icons';
import assignTaskService from '../services/assignTaskService';
import taskService from '../services/taskService';
import dayjs from 'dayjs';
import { message } from 'antd';
import { formatDate } from '../utils/date';

const { Text, Title } = Typography;

const TaskAssign = () => {
    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [updatingTaskId, setUpdatingTaskId] = useState(null);

    const handleToggleComplete = async (task) => {
        try {
            setUpdatingTaskId(task.id);
            const newStatus =
                task.status === 'completed' ? 'needsAction' : 'completed';
            const isCompleted = newStatus === 'completed';

            await taskService.toggleTaskComplete(
                task.listId,
                task.id,
                isCompleted,
                task.taskList?.board?.id
            );

            // Cập nhật state local
            setTasks(
                tasks.map((t) => {
                    if (t.id === task.id) {
                        return { ...t, status: newStatus };
                    }
                    return t;
                })
            );

            message.success(
                newStatus === 'completed'
                    ? 'Đã hoàn thành công việc!'
                    : 'Đã đánh dấu công việc chưa hoàn thành!'
            );
        } catch (error) {
            console.error('Lỗi khi cập nhật trạng thái công việc:', error);
            message.error(
                'Không thể cập nhật trạng thái công việc. Vui lòng thử lại!'
            );
        } finally {
            setUpdatingTaskId(null);
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high':
                return '#ff4d4f';
            case 'medium':
                return '#faad14';
            case 'low':
                return '#52c41a';
            default:
                return '#d9d9d9';
        }
    };

    const fetchUserAssignedTasks = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await assignTaskService.getUserAssignedTasks();
            setTasks(response || []);
        } catch (err) {
            console.error('Lỗi khi tải danh sách công việc được giao:', err);
            setError('Không thể tải danh sách công việc được giao');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchUserAssignedTasks();
    }, []);

    if (loading) {
        return (
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '200px',
                    flexDirection: 'column',
                    gap: '16px'
                }}
            >
                <Spin size='large' />
                <Text type='secondary'>Đang tải danh sách công việc...</Text>
            </div>
        );
    }

    if (error) {
        return (
            <div style={{ padding: '24px', textAlign: 'center' }}>
                <Empty
                    description={error}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    if (!tasks || tasks.length === 0) {
        return (
            <div style={{ padding: '24px' }}>
                <div style={{ marginBottom: '24px' }}>
                    <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                        Công việc được giao cho bạn
                    </Title>
                    <Text type='secondary' style={{ fontSize: '14px' }}>
                        Danh sách các công việc được giao và theo dõi tiến độ
                    </Text>
                </div>
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%'
                    }}
                >
                    <Empty
                        description='Chưa có công việc nào được giao cho bạn'
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                </div>
            </div>
        );
    }

    return (
        <div style={{ padding: '0' }}>
            <div style={{ marginBottom: '24px' }}>
                <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                    Công việc được giao cho bạn
                </Title>
                <Text type='secondary' style={{ fontSize: '14px' }}>
                    Danh sách các công việc được giao và theo dõi tiến độ (
                    {tasks.length} công việc)
                </Text>
            </div>

            <Space direction='vertical' size='middle' style={{ width: '100%' }}>
                {tasks.map((task) => {
                    const isCompleted = task.status === 'completed';

                    return (
                        <Card
                            key={task.id}
                            size='small'
                            style={{
                                marginBottom: 8,
                                position: 'relative',
                                backgroundColor: isCompleted
                                    ? '#f5f5f5'
                                    : '#ffffff',
                                opacity: isCompleted ? 0.7 : 1,
                                border: '1px solid #d9d9d9',
                                borderRadius: '6px',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
                                transition: 'all 0.2s ease',
                                cursor: 'pointer'
                            }}
                            className={`task-card ${
                                isCompleted ? 'completed-task' : ''
                            }`}
                            hoverable
                            styles={{ body: { padding: '12px' } }}
                            onMouseEnter={(e) => {
                                if (!isCompleted) {
                                    e.currentTarget.style.boxShadow =
                                        '0 4px 12px rgba(0,0,0,0.15)';
                                    e.currentTarget.style.transform =
                                        'translateY(-1px)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.boxShadow =
                                    '0 2px 8px rgba(0,0,0,0.06)';
                                e.currentTarget.style.transform =
                                    'translateY(0)';
                            }}
                        >
                            <div
                                className='task-header'
                                style={{
                                    display: 'flex',
                                    alignItems: 'flex-start',
                                    gap: '8px',
                                    marginBottom: '8px'
                                }}
                            >
                                {/* Checkbox */}
                                <Checkbox
                                    checked={isCompleted}
                                    style={{ marginTop: '2px' }}
                                    onChange={() => handleToggleComplete(task)}
                                    disabled={updatingTaskId === task.id}
                                />

                                {/* Priority indicator */}
                                <div
                                    className='task-priority'
                                    style={{
                                        width: '4px',
                                        height: '16px',
                                        backgroundColor: getPriorityColor(
                                            task.priority
                                        ),
                                        borderRadius: '2px',
                                        marginTop: '2px',
                                        flexShrink: 0
                                    }}
                                />

                                {/* Task Content */}
                                <div style={{ flex: 1 }}>
                                    {/* Title and Status */}
                                    <div
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '8px',
                                            marginBottom: '8px'
                                        }}
                                    >
                                        <Text
                                            strong
                                            className='task-title'
                                            style={{
                                                fontSize: '14px',
                                                color: isCompleted
                                                    ? '#8c8c8c'
                                                    : '#262626',
                                                textDecoration: isCompleted
                                                    ? 'line-through'
                                                    : 'none',
                                                lineHeight: '1.4',
                                                flex: 1
                                            }}
                                        >
                                            {task.title}
                                        </Text>
                                        {isCompleted ? (
                                            <Tag
                                                color='success'
                                                icon={
                                                    <CheckCircleTwoTone twoToneColor='#52c41a' />
                                                }
                                                style={{
                                                    fontSize: '11px',
                                                    lineHeight: '18px',
                                                    padding: '0 6px',
                                                    borderRadius: '4px'
                                                }}
                                            >
                                                HOÀN THÀNH
                                            </Tag>
                                        ) : (
                                            <Tag
                                                color='processing'
                                                icon={<ClockCircleOutlined />}
                                                style={{
                                                    fontSize: '11px',
                                                    lineHeight: '18px',
                                                    padding: '0 6px',
                                                    borderRadius: '4px'
                                                }}
                                            >
                                                ĐANG LÀM
                                            </Tag>
                                        )}
                                    </div>

                                    {/* Workspace, Board and TaskList info */}
                                    <div
                                        className='task-details'
                                        style={{ marginBottom: '8px' }}
                                    >
                                        <Space size={[4, 4]} wrap>
                                            {task.taskList?.board
                                                ?.workspace && (
                                                <Tag
                                                    style={{
                                                        backgroundColor:
                                                            '#f3e8ff',
                                                        color: isCompleted
                                                            ? '#a6a6a6'
                                                            : '#722ed1',
                                                        border: '1px solid #d3adf7',
                                                        borderRadius: '4px',
                                                        fontSize: '11px',
                                                        padding: '2px 6px',
                                                        lineHeight: '16px'
                                                    }}
                                                >
                                                    {' '}
                                                    {
                                                        task.taskList.board
                                                            .workspace.name
                                                    }
                                                </Tag>
                                            )}
                                            {task.taskList?.board && (
                                                <Tag
                                                    style={{
                                                        backgroundColor:
                                                            '#e6f7ff',
                                                        color: isCompleted
                                                            ? '#a6a6a6'
                                                            : '#1890ff',
                                                        border: '1px solid #91d5ff',
                                                        borderRadius: '4px',
                                                        fontSize: '11px',
                                                        padding: '2px 6px',
                                                        lineHeight: '16px'
                                                    }}
                                                >
                                                    {' '}
                                                    {task.taskList.board.name}
                                                </Tag>
                                            )}
                                            {task.taskList && (
                                                <Tag
                                                    style={{
                                                        backgroundColor:
                                                            '#f6ffed',
                                                        color: isCompleted
                                                            ? '#a6a6a6'
                                                            : '#52c41a',
                                                        border: '1px solid #b7eb8f',
                                                        borderRadius: '4px',
                                                        fontSize: '11px',
                                                        padding: '2px 6px',
                                                        lineHeight: '16px'
                                                    }}
                                                >
                                                    {task.taskList.title}
                                                </Tag>
                                            )}
                                        </Space>
                                    </div>

                                    {/* Creator, Due date and Assigned date */}
                                    <div
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            flexWrap: 'wrap',
                                            gap: '8px'
                                        }}
                                    >
                                        <div
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '6px'
                                            }}
                                        >
                                            <Avatar
                                                size={16}
                                                src={task.creator?.photo_url}
                                                icon={<UserOutlined />}
                                                style={{
                                                    backgroundColor: '#1890ff',
                                                    fontSize: '10px'
                                                }}
                                            />
                                            <Text
                                                type='secondary'
                                                style={{
                                                    fontSize: '12px',
                                                    color: isCompleted
                                                        ? '#a6a6a6'
                                                        : '#8c8c8c'
                                                }}
                                            >
                                                Tạo bởi{' '}
                                                <strong>
                                                    {task.creator?.full_name ||
                                                        task.creator?.email}
                                                </strong>
                                            </Text>
                                        </div>
                                        <div
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '12px'
                                            }}
                                        >
                                            <Text
                                                type='secondary'
                                                style={{
                                                    fontSize: '11px',
                                                    color: isCompleted
                                                        ? '#a6a6a6'
                                                        : '#8c8c8c'
                                                }}
                                            >
                                                {'Ngày tạo: '}
                                                {dayjs(task.createdAt).format(
                                                    'DD/MM/YYYY'
                                                )}
                                            </Text>
                                            {task.due && (
                                                <Text
                                                    type='secondary'
                                                    style={{
                                                        fontSize: '11px',
                                                        color: isCompleted
                                                            ? '#a6a6a6'
                                                            : '#8c8c8c'
                                                    }}
                                                >
                                                    {'Hạn chót: '}
                                                    {formatDate(task.due)}
                                                </Text>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    );
                })}
            </Space>
        </div>
    );
};

export default TaskAssign;
