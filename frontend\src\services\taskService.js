import axios from '../utils/axiosCustomize';

const getAllTasks = async (taskListId) => {
        const response = await axios.get(`/tasklists/${taskListId}/tasks`);
        return response.data;
}

const getTask = async (taskListId, taskId, boardId) => {
        const response = await axios.get(`/tasklists/${taskListId}/tasks/${taskId}`, {
            params: {
                boardId: boardId
            }
        });
        return response.data;
}

const createTask = async (taskListId, newTask, boardId) => {
        const response = await axios.post(`/tasklists/${taskListId}/tasks`, {
            newTask,
            boardId
        });
        return response.data;
}

const createSubtask = async (taskListId, parentTaskId, newTask, boardId) => {
        const response = await axios.post(`/tasklists/${taskListId}/tasks/${parentTaskId}/subtask`, {
            newTask,
            boardId
        });
        return response.data;
}

const updateTask = async (taskListId, taskId, task, boardId) => {
        const response = await axios.put(`/tasklists/${taskListId}/tasks/${taskId}`, {
            ...task,
            boardId: boardId
        });
        return response.data;
}

const deleteTask = async (taskListId, taskId, boardId) => {
        const response = await axios.delete(`/tasklists/${taskListId}/tasks/${taskId}`, {
            data: {
                boardId: boardId
            }
        });
        return response.data;       
}

const completeTask = async (taskListId, taskId, boardId) => {
        const response = await axios.post(`/tasklists/${taskListId}/tasks/${taskId}/complete`, {
            boardId: boardId
        });
        return response.data;
}

const unCompleteTask = async (taskListId, taskId, boardId) => {
        const response = await axios.post(`/tasklists/${taskListId}/tasks/${taskId}/uncomplete`, {
            boardId: boardId
        });
        return response.data;
}

const clearCompletedTasks = async (taskListId) => {
        const response = await axios.put(`/tasklists/${taskListId}/tasks/clear`);
        return response.data;
}

const moveTask = async (taskListId, taskId, newTaskListId, previous, next, parent, boardId) => {
        const response = await axios.post(`/tasklists/${taskListId}/tasks/${taskId}/move`, {
            newTaskListId,
            previous,
            next,
            parent,
            boardId
        });
        return response.data;
}

const toggleTaskComplete = async (taskListId, taskId, isCompleted, boardId) => {
        if (isCompleted) {
            // Đánh dấu hoàn thành
            const response = await axios.put(`/tasklists/${taskListId}/tasks/${taskId}/complete`, {
                boardId: boardId
            });
            return response.data;
        } else {
            // Bỏ đánh dấu hoàn thành - cập nhật status về needsAction
            const response = await axios.put(`/tasklists/${taskListId}/tasks/${taskId}/uncomplete`, {
                boardId: boardId
            });
            return response.data;
        }
}

const searchTasks = async (query, boardId) => {
    const response = await axios.get(`/tasklists/tasks/search`, {
        params: {
            query: query,
            boardId: boardId
        }
    });
    return response.data;
}

export default {
    getAllTasks,
    getTask,
    createTask,
    createSubtask,
    updateTask,
    deleteTask,
    completeTask,
    unCompleteTask,
    clearCompletedTasks,
    moveTask,
    toggleTaskComplete,
    searchTasks
}