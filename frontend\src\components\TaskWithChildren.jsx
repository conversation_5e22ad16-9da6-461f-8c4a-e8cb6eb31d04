import React, { useState } from 'react';
import TaskCard from './TaskCard';

// Component để render công việc cha và các công việc con (chỉ 1 cấp)
const TaskWithChildren = ({
    task,
    listId,
    boardId,
    workspaceId,
    onUpdateTask,
    onDeleteTask,
    onAddSubtask,
    onToggleTaskComplete,
    onModalStateChange,
    boardMembers = null,
    searchText,
    setSearchResult
}) => {
    // State để quản lý việc thu gọn/mở rộng công việc con
    const [isExpanded, setIsExpanded] = useState(true);

    // Hàm toggle trạng thái thu gọn/mở rộng
    const handleToggleExpanded = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <>
            {/* Công việc cha */}
            <TaskCard
                key={task.id}
                task={task}
                listId={listId}
                boardId={boardId}
                workspaceId={workspaceId}
                level={0}
                isParent={task.children && task.children.length > 0}
                childrenTasks={task.children || []}
                onUpdateTask={onUpdateTask}
                onDeleteTask={onDeleteTask}
                onAddSubtask={onAddSubtask}
                onToggleTaskComplete={onToggleTaskComplete}
                // Thêm props để xử lý thu gọn/mở rộng
                isExpanded={isExpanded}
                onToggleExpanded={handleToggleExpanded}
                onModalStateChange={onModalStateChange}
                boardMembers={boardMembers}
                searchText={searchText}
                setSearchResult={setSearchResult}
            />

            {/* Công việc con (chỉ hiển thị khi được mở rộng) */}
            {isExpanded && task.children && task.children.length > 0 && (
                <div
                    className='task-children-container'
                    style={{
                        position: 'relative',
                        paddingLeft: '20px',
                        marginTop: '4px'
                    }}
                >
                    {task.children.map((childTask) => (
                        <TaskCard
                            key={childTask.id}
                            boardId={boardId}
                            workspaceId={workspaceId}
                            task={childTask}
                            listId={listId}
                            level={1}
                            isChildTask={true}
                            onUpdateTask={onUpdateTask}
                            onDeleteTask={onDeleteTask}
                            onAddSubtask={null}
                            onToggleTaskComplete={onToggleTaskComplete}
                            onModalStateChange={onModalStateChange}
                            boardMembers={boardMembers}
                            searchText={searchText}
                            setSearchResult={setSearchResult}
                        />
                    ))}
                </div>
            )}
        </>
    );
};

export default TaskWithChildren;
