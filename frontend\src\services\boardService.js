import axios from '../utils/axiosCustomize';

const getBoards = async () => {
        const response  = await axios.get(`/boards`);
        return response.data;
}

const createBoard = async (boardData) => {
        const response = await axios.post(`/boards`, boardData);
        return response.data;
}

const updateBoard = async (boardId, board) => {
        const response = await axios.put(`/boards/${boardId}`, board);
        return response.data;
}

const deleteBoard = async (boardId) => {
        const response = await axios.delete(`/boards/${boardId}`);
        return response.data;
}

const getBoardsByWorkspaceId = async (workspaceId) => {
        const response = await axios.get(`/boards/workspace/${workspaceId}`);
        return response.data;
}

export default {
    getBoards,
    createBoard, 
    updateBoard,
    deleteBoard,
    getBoardsByWorkspaceId
}