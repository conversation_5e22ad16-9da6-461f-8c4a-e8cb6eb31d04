const path = require('path');

if (process.env.NODE_ENV === 'production') {
    require('dotenv').config({ path: path.resolve(__dirname, '../.env.production') });
  } else {
    require('dotenv').config();
  }


const express = require('express');
const session = require('express-session');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const passport = require('./config/passport');
const authRoutes = require('./routes/authRoutes');
const { sequelize } = require('./models');
const { syncDatabase } = require('./utils/dbSync');

const taskListRoutes = require('./routes/taskListRoutes');
const taskRoutes = require('./routes/taskRoutes');
const boardRoutes = require('./routes/boardRoutes');
const taskAssignRoutes = require('./routes/taskAssignRoutes');
const userRoutes = require('./routes/userRoutes');
const attachmentRoutes = require('./routes/attachmentRoutes');
const exportRoutes = require('./routes/exportRoutes');
const boardAssignRoutes = require('./routes/boardAssignRoutes');
const workspaceRoutes = require('./routes/workspaceRoutes');
const workspaceAssignRoutes = require('./routes/workspaceAssignRoutes');
const asyncRoutes = require('./routes/asyncRoutes');
const notificationRoutes = require('./routes/notificationRoutes');

const cors = require('cors');
const app = express();

// Trust proxy for production (behind reverse proxy/load balancer)
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', 1);
}

// Middleware
app.use(morgan('dev'));
app.use(helmet());
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS Configuration
const allowedOrigins = process.env.NODE_ENV === 'production'
    ? ['https://gwtask.com', 'https://www.gwtask.com']
    : ['http://localhost:5173', 'http://localhost:3000'];

app.use(cors({
  origin: function(origin, callback){
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    } else {
      console.error(`CORS Error: Origin ${origin} not allowed`);
      return callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'ngrok-skip-browser-warning'],
  preflightContinue: true,
  optionsSuccessStatus: 200
}));

// Session middleware
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'yourSecret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax'
  }
};

if (process.env.NODE_ENV === 'production') {
  sessionConfig.cookie.domain = '.gwtask.com';
}

app.use(session(sessionConfig));

app.use(passport.initialize());
app.use(passport.session()); // Bắt buộc để isAuthenticated() hoạt động

// Test Database Connection
const testDbConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log("Kết nối cơ sở dữ liệu thành công.");

    // Đồng bộ database
    await syncDatabase();

  } catch (error) {
    console.error("Kết nối cơ sở dữ liệu thất bại:", error);
  }
};

testDbConnection();

//test
app.get('/api', (req, res) => {
  res.send('Hello World');
});


// Routes
app.use('/api/auth', authRoutes);
app.use('/api/tasklists', taskListRoutes);
app.use('/api/tasklists', taskRoutes);
app.use('/api/boards', boardRoutes);
app.use('/api/assign', taskAssignRoutes);
app.use('/api/users', userRoutes);
app.use('/api/task-attachments', attachmentRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/board-assign', boardAssignRoutes);
app.use('/api/workspace', workspaceRoutes);
app.use('/api/workspace-assign', workspaceAssignRoutes);
app.use('/api/async', asyncRoutes);
app.use('/api/notifications', notificationRoutes);



if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../frontend/dist')));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, "frontend", "dist", "index.html"));
  });
}

// Global error handler for production
if (process.env.NODE_ENV === 'production') {
  app.use((err, req, res, next) => {
    console.error('Production Error:', err.stack);
    res.status(500).json({
      status: 'error',
      message: 'Có lỗi xảy ra trên server'
    });
  });
}

module.exports = app; 