const notificationService = require('../services/notificationService');

class NotificationController {
    async getNotifications(req, res) {
        try {
            const userId = req.user.id;
            const notifications = await notificationService.getNotificationsByUserId(userId);
            
            res.status(200).json({
                success: true,
                data: notifications
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    async deleteNotification(req, res) {
        try {
            const { notificationId } = req.params;
            const userId = req.user.id;
            
            await notificationService.deleteNotification(notificationId, userId);
            
            res.status(200).json({
                success: true,
                message: 'Notification deleted successfully'
            });
        } catch (error) {
            res.status(error.message === 'Notification not found' ? 404 : 500).json({
                success: false,
                message: error.message
            });
        }
    }

    async markAsRead(req, res) {
        try {
            const { notificationId } = req.params;
            const userId = req.user.id;

            await notificationService.markAsRead(notificationId, userId);

            res.status(200).json({
                success: true,
                message: 'Notification marked as read successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    async markAllAsRead(req, res) {
        try {
            const userId = req.user.id;
            await notificationService.markAllAsRead(userId);

            res.status(200).json({
                success: true,
                message: 'All notifications marked as read successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    async deleteAllNotifications(req, res) {
        try {
            const userId = req.user.id;
            await notificationService.deleteAllNotifications(userId);

            res.status(200).json({
                success: true,
                message: 'All notifications deleted successfully'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }       
    }   
}

module.exports = new NotificationController(); 