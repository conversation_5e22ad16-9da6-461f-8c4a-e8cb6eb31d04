import { useState, useEffect, useCallback } from "react";
import BoardService from "../services/boardService";
import {
    addAssigneeBoard,
    removeAssigneeFromBoard,
    getBoardAssignees,
} from "../services/boardAssignService";
import { useMessage } from "../contexts/MessageContext";
import { useSocket } from "../contexts/SocketContext";
import { 
    getSelectedBoard, 
    saveSelectedBoard, 
    saveWorkspaceBoardState 
} from "../utils/stateManager";

const useBoard = (selectedWorkspace) => {
    const message = useMessage();
    const { joinBoard, leaveBoard } = useSocket();
    const [boards, setBoards] = useState([]);
    const [selectedBoard, setSelectedBoard] = useState(() => {
        // Khôi phục board từ localStorage khi khởi tạo
        return selectedWorkspace ? getSelectedBoard(selectedWorkspace) : null;
    });
    const [loading, setLoading] = useState(true);
    const [boardAssignees, setBoardAssignees] = useState([]);
    const [isCreatingBoard, setIsCreatingBoard] = useState(false);
    const [isUpdatingBoard, setIsUpdatingBoard] = useState(false);
    const [isDeletingBoard, setIsDeletingBoard] = useState(false);
    const { success, error: showError } = useMessage();

    const fetchBoards = useCallback(async () => {
        if (!selectedWorkspace) {
            setBoards([]);
            setSelectedBoard(null);
            return;
        }

        try {
            setLoading(true);
            const response = await BoardService.getBoardsByWorkspaceId(
                selectedWorkspace
            );
            setBoards(response);
            // console.log('Boards response:', response);

            // Khôi phục board từ localStorage hoặc chọn board đầu tiên
            if (response.length > 0) {
                const savedBoardId = getSelectedBoard(selectedWorkspace);
                const boardExists = savedBoardId && response.find(board => board.id === savedBoardId);
                
                if (boardExists) {
                    setSelectedBoard(savedBoardId);
                } else {
                    setSelectedBoard(response[0].id);
                }
            } else {
                setSelectedBoard(null);
            }
        } catch (error) {
            console.error("Lỗi khi tải danh sách bảng:", error);
            showError("Không thể tải danh sách bảng");
            setSelectedBoard(null);
        } finally {
            setLoading(false);
        }
    }, [selectedWorkspace, message]);

    const createBoard = useCallback(
        async (workspaceId) => {
            try {
                if (!workspaceId) {
                    message.error("Vui lòng chọn workspace trước");
                    return;
                }

                setIsCreatingBoard(true);
                const response = await BoardService.createBoard({
                    workspaceId: workspaceId,
                    name: "Bảng mới",
                });

                setBoards((prevBoards) => [...prevBoards, response]);
                setSelectedBoard(response.id);
                saveSelectedBoard(selectedWorkspace, response.id);
                saveWorkspaceBoardState(selectedWorkspace, response.id);
                success("Tạo bảng thành công!");
            } catch (error) {
                console.error("Lỗi khi tạo bảng:", error);
                showError("Không thể tạo bảng: " + (error.response?.data?.message || error.message));
            } finally {
                setIsCreatingBoard(false);
            }
        },
        [message]
    );

    const updateBoardName = useCallback(
        async (boardId, newName) => {
            try {

                //Nếu để trống tên bảng thì không cập nhật
                if (newName === "") {
                    showError("Tên bảng không được để trống");
                    return;
                }

                setIsUpdatingBoard(true);
                await BoardService.updateBoard(boardId, { name: newName });
                setBoards((prevBoards) =>
                    prevBoards.map((board) =>
                        board.id === boardId
                            ? { ...board, name: newName }
                            : board
                    )
                );
                success("Cập nhật tên bảng thành công!");
            } catch (error) {
                console.error("Lỗi khi cập nhật tên bảng:", error);
                showError("Không thể cập nhật tên bảng: " + (error.response?.data?.message || error.message));
            } finally {
                setIsUpdatingBoard(false);
            }
        },
        [message]
    );

    const deleteBoard = useCallback(
        async (boardId) => {
            try {
                setIsDeletingBoard(true);

                // Kiểm tra xem có phải board mặc định không
                const isDefaultBoard = boards.find(
                    (board) => board.id === boardId && board.isDefault
                );
                if (isDefaultBoard) {
                    showError("Không thể xóa bảng mặc định");
                    return;
                }

                await BoardService.deleteBoard(boardId);

                setBoards((prevBoards) => {
                    const updatedBoards = prevBoards.filter(
                        (board) => board.id !== boardId
                    );
                    if (selectedBoard === boardId && updatedBoards.length > 0) {
                        const newBoardId = updatedBoards[0].id;
                        setSelectedBoard(newBoardId);
                        saveSelectedBoard(selectedWorkspace, newBoardId);
                        saveWorkspaceBoardState(selectedWorkspace, newBoardId);
                    } else if (updatedBoards.length === 0) {
                        setSelectedBoard(null);
                        saveSelectedBoard(selectedWorkspace, null);
                        saveWorkspaceBoardState(selectedWorkspace, null);
                    }
                    return updatedBoards;
                });

                success("Xóa bảng thành công!");
            } catch (error) {
                console.error("Lỗi khi xóa bảng:", error);
                showError(
                    "Không thể xóa bảng: " +
                        (error.response?.data?.message || error.message)
                );
            } finally {
                setIsDeletingBoard(false);
            }
        },
        [selectedBoard, message]
    );

    const fetchBoardAssignees = useCallback(async (boardId) => {
        if (!boardId) return;

        try {
            const assignees = await getBoardAssignees(boardId);
            setBoardAssignees(assignees || []);
        } catch (error) {
            console.error("Lỗi khi tải danh sách thành viên bảng:", error);
            setBoardAssignees([]);
        }
    }, []);

    // Xử lý join/leave board room
    useEffect(() => {
        if (selectedBoard) {
            // Join board room khi board được chọn
            joinBoard(selectedBoard);

            // Leave board room khi board thay đổi hoặc component unmount
            return () => {
                leaveBoard(selectedBoard);
            };
        }
    }, [selectedBoard, joinBoard, leaveBoard]);

    const refreshBoards = useCallback(async () => {
        if (!selectedWorkspace) return;

        try {
            const response = await BoardService.getBoardsByWorkspaceId(
                selectedWorkspace
            );
            setBoards(response);
        } catch (error) {
            console.error("Lỗi khi refresh danh sách bảng:", error);
        }
    }, [selectedWorkspace]);

    const handleAddBoardAssignee = useCallback(
        async (boardId, user) => {
            try {
                await addAssigneeBoard(boardId, user.email);
                await fetchBoardAssignees(boardId);
                success("Thêm thành viên vào bảng thành công!");
            } catch (error) {
                console.error("Lỗi khi thêm thành viên vào bảng:", error);
                showError("Không thể thêm thành viên vào bảng");
            }
        },
        [message, fetchBoardAssignees]
    );

    const handleRemoveBoardAssignee = useCallback(
        async (boardId, assignee, forceRefresh = false) => {
            try {
                if (!forceRefresh) {
                    const email = assignee.user?.email;
                    await removeAssigneeFromBoard(boardId, email);
                    success("Xóa thành viên khỏi bảng thành công!");
                }
                await fetchBoardAssignees(boardId);
            } catch (error) {
                console.error("Lỗi khi xóa thành viên khỏi bảng:", error);
                if (!forceRefresh) {
                    showError("Không thể xóa thành viên khỏi bảng");
                }
            }
        },
        [message, fetchBoardAssignees]
    );

    useEffect(() => {
        fetchBoards();
    }, [fetchBoards]);

    // Wrapper function để cập nhật selectedBoard và lưu vào localStorage
    const updateSelectedBoard = useCallback((boardId) => {
        setSelectedBoard(boardId);
        if (selectedWorkspace) {
            saveSelectedBoard(selectedWorkspace, boardId);
            saveWorkspaceBoardState(selectedWorkspace, boardId);
        }
    }, [selectedWorkspace]);

    useEffect(() => {
        if (selectedBoard) {
            fetchBoardAssignees(selectedBoard);
        }
    }, [selectedBoard, fetchBoardAssignees]);

    return {
        boards,
        selectedBoard,
        loading,
        boardAssignees,
        isCreatingBoard,
        isUpdatingBoard,
        isDeletingBoard,
        setSelectedBoard: updateSelectedBoard,
        fetchBoards,
        refreshBoards,
        createBoard,
        updateBoardName,
        deleteBoard,
        handleAddBoardAssignee,
        handleRemoveBoardAssignee,
        fetchBoardAssignees,
    };
};

export default useBoard;
