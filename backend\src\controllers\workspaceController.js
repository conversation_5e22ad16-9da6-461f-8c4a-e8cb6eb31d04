const workspaceService = require('../services/workspaceService');

const createWorkspace = async (req, res) => {
    try {
        const { name, description } = req.body;
        
        const workspace = await workspaceService.createWorkspace(name, description, req.user.id);
        
        res.status(201).json(workspace);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getWorkspaces = async (req, res) => {
    try {
        const workspaces = await workspaceService.getWorkspacesByOwner(req.user.id);
        res.status(200).json(workspaces);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getWorkspaceById = async (req, res) => {
    try {
        const { id } = req.params;
        
        const workspace = await workspaceService.getWorkspaceById(id, req.user.id);
        
        res.status(200).json(workspace);
    } catch (error) {
        if (error.message.includes('không tồn tại') || error.message.includes('không có quyền')) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

const updateWorkspace = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description } = req.body;

        const updateData = {};
        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;

        const workspace = await workspaceService.updateWorkspace(id, updateData, req.user.id);
        
        res.status(200).json(workspace);
    } catch (error) {
        if (error.message.includes('không tồn tại') || error.message.includes('không có quyền')) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

const deleteWorkspace = async (req, res) => {
    try {
        const { id } = req.params;
        
        const result = await workspaceService.deleteWorkspace(id, req.user.id);
        
        res.status(200).json(result);
    } catch (error) {
        if (error.message.includes('không tồn tại') || error.message.includes('không có quyền')) {
            return res.status(404).json({ message: error.message });
        }
        if (error.message.includes('Không thể xóa workspace có chứa board')) {
            return res.status(400).json({ message: error.message });
        }
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    createWorkspace,
    getWorkspaces,
    getWorkspaceById,
    updateWorkspace,
    deleteWorkspace
}; 